#!/usr/bin/env python3
"""
动态图表绘制器
实现每20个episode的动态更新机制和实时可视化
"""

import os
import time
import json
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Optional
from datetime import datetime
from collections import deque

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['AR PL UMing CN', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class DynamicPlotter:
    """动态图表绘制器"""
    
    def __init__(self, save_dir: str = "results/dynamic_plots", 
                 update_interval: int = 20, max_points: int = 1000):
        """
        初始化动态绘制器
        
        Args:
            save_dir: 保存目录
            update_interval: 更新间隔（episodes）
            max_points: 最大显示点数
        """
        self.save_dir = save_dir
        self.update_interval = update_interval
        self.max_points = max_points
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 数据存储
        self.data_buffer = {
            'episodes': deque(maxlen=max_points),
            'rewards': deque(maxlen=max_points),
            'success_rates': deque(maxlen=max_points),
            'episode_lengths': deque(maxlen=max_points),
            'stages': deque(maxlen=max_points),
            'loss_values': deque(maxlen=max_points),
            'learning_rates': deque(maxlen=max_points)
        }
        
        # 图表状态
        self.fig = None
        self.axes = None
        self.lines = {}
        self.is_plotting = False
        self.last_update_episode = 0
        
        # 颜色配置
        self.colors = {
            'reward': '#2E86AB',
            'success': '#A23B72',
            'length': '#F18F01',
            'loss': '#C73E1D',
            'lr': '#6A994E'
        }
        
        print(f"📈 动态绘制器初始化完成")
        print(f"  保存目录: {save_dir}")
        print(f"  更新间隔: {update_interval} episodes")
        print(f"  最大点数: {max_points}")

    def update_save_dir(self, new_save_dir: str):
        """
        更新保存目录

        Args:
            new_save_dir: 新的保存目录路径
        """
        self.save_dir = new_save_dir
        os.makedirs(new_save_dir, exist_ok=True)
        print(f"📁 动态绘制器保存目录已更新: {new_save_dir}")
    
    def add_data_point(self, episode: int, reward: float, success_rate: float,
                      episode_length: int, stage: int, loss_value: float = 0.0,
                      learning_rate: float = 0.0) -> None:
        """
        添加数据点
        
        Args:
            episode: episode编号
            reward: 奖励值
            success_rate: 成功率
            episode_length: episode长度
            stage: 当前阶段
            loss_value: 损失值
            learning_rate: 学习率
        """
        # 添加数据到缓冲区
        self.data_buffer['episodes'].append(episode)
        self.data_buffer['rewards'].append(reward)
        self.data_buffer['success_rates'].append(success_rate)
        self.data_buffer['episode_lengths'].append(episode_length)
        self.data_buffer['stages'].append(stage)
        self.data_buffer['loss_values'].append(loss_value)
        self.data_buffer['learning_rates'].append(learning_rate)
        
        # 检查是否需要更新图表
        if episode - self.last_update_episode >= self.update_interval:
            self.update_dynamic_plots()
            self.last_update_episode = episode
    
    def initialize_plots(self) -> None:
        """初始化动态图表"""
        try:
            if self.fig is not None:
                plt.close(self.fig)

            # 创建子图
            self.fig, self.axes = plt.subplots(2, 3, figsize=(18, 10))
            self.fig.suptitle('实时训练监控', fontsize=16, fontweight='bold')

            # 初始化各个子图
            self._init_reward_plot()
            self._init_success_rate_plot()
            self._init_episode_length_plot()
            self._init_loss_plot()
            self._init_learning_rate_plot()
            self._init_stage_progress_plot()

            # 调整布局
            plt.tight_layout()

            # 检测GUI环境并启用交互模式
            try:
                import matplotlib
                backend = matplotlib.get_backend()
                if backend.lower() != 'agg':  # 非无头模式
                    plt.ion()
                    plt.show()
                    print("📊 动态图表已初始化（交互模式）")
                else:
                    print("📊 动态图表已初始化（无头模式）")
            except Exception as gui_e:
                print(f"⚠️  GUI初始化警告: {gui_e}")
                print("📊 动态图表已初始化（静态模式）")

            self.is_plotting = True

        except Exception as e:
            print(f"❌ 动态图表初始化失败: {e}")
            self.is_plotting = False
            self.fig = None
            self.axes = None
    
    def _init_reward_plot(self) -> None:
        """初始化奖励图表"""
        ax = self.axes[0, 0]
        line, = ax.plot([], [], color=self.colors['reward'], linewidth=2, label='奖励')
        line_ma, = ax.plot([], [], color='red', linewidth=2, linestyle='--', label='移动平均')
        
        self.lines['reward'] = line
        self.lines['reward_ma'] = line_ma
        
        ax.set_title('训练奖励', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('奖励')
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    def _init_success_rate_plot(self) -> None:
        """初始化成功率图表"""
        ax = self.axes[0, 1]
        line, = ax.plot([], [], color=self.colors['success'], linewidth=2, 
                       marker='o', markersize=2, label='成功率')
        
        self.lines['success'] = line
        
        ax.set_title('成功率', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('成功率')
        ax.set_ylim(0, 1)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # 添加目标线
        ax.axhline(y=0.8, color='red', linestyle='--', alpha=0.7, label='目标(0.8)')
    
    def _init_episode_length_plot(self) -> None:
        """初始化episode长度图表"""
        ax = self.axes[0, 2]
        line, = ax.plot([], [], color=self.colors['length'], linewidth=2, label='Episode长度')
        
        self.lines['length'] = line
        
        ax.set_title('Episode长度', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('步数')
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    def _init_loss_plot(self) -> None:
        """初始化损失图表"""
        ax = self.axes[1, 0]
        line, = ax.plot([], [], color=self.colors['loss'], linewidth=2, label='损失')
        
        self.lines['loss'] = line
        
        ax.set_title('训练损失', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('损失')
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    def _init_learning_rate_plot(self) -> None:
        """初始化学习率图表"""
        ax = self.axes[1, 1]
        line, = ax.plot([], [], color=self.colors['lr'], linewidth=2, label='学习率')
        
        self.lines['lr'] = line
        
        ax.set_title('学习率', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('学习率')
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    def _init_stage_progress_plot(self) -> None:
        """初始化阶段进度图表"""
        ax = self.axes[1, 2]
        
        ax.set_title('课程学习进度', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('阶段')
        ax.grid(True, alpha=0.3)
    
    def update_dynamic_plots(self) -> None:
        """更新动态图表"""
        if not self.is_plotting or len(self.data_buffer['episodes']) < 2:
            return

        if self.fig is None or self.axes is None:
            print("⚠️  图表未初始化，跳过更新")
            return

        try:
            # 获取数据
            episodes = list(self.data_buffer['episodes'])
            rewards = list(self.data_buffer['rewards'])
            success_rates = list(self.data_buffer['success_rates'])
            episode_lengths = list(self.data_buffer['episode_lengths'])
            stages = list(self.data_buffer['stages'])
            loss_values = list(self.data_buffer['loss_values'])
            learning_rates = list(self.data_buffer['learning_rates'])

            # 更新各个图表
            self._update_reward_plot(episodes, rewards)
            self._update_success_rate_plot(episodes, success_rates)
            self._update_episode_length_plot(episodes, episode_lengths)
            self._update_loss_plot(episodes, loss_values)
            self._update_learning_rate_plot(episodes, learning_rates)
            self._update_stage_progress_plot(episodes, stages)

            # 尝试刷新图表（可能在无GUI环境中失败）
            try:
                if hasattr(self.fig, 'canvas'):
                    self.fig.canvas.draw()
                    self.fig.canvas.flush_events()
            except Exception as canvas_e:
                # 在无GUI环境中canvas操作可能失败，这是正常的
                pass

            # 保存当前图表
            self._save_current_plot()

        except Exception as e:
            print(f"⚠️ 更新动态图表时出错: {e}")
            # 不要停止整个训练过程，只是记录错误
    
    def _update_reward_plot(self, episodes: List[int], rewards: List[float]) -> None:
        """更新奖励图表"""
        # 更新原始奖励线
        self.lines['reward'].set_data(episodes, rewards)
        
        # 计算并更新移动平均
        if len(rewards) > 10:
            window_size = min(50, len(rewards) // 4)
            moving_avg = np.convolve(rewards, np.ones(window_size)/window_size, mode='valid')
            moving_episodes = episodes[window_size-1:]
            self.lines['reward_ma'].set_data(moving_episodes, moving_avg)
        
        # 自动调整坐标轴
        ax = self.axes[0, 0]
        ax.relim()
        ax.autoscale_view()
    
    def _update_success_rate_plot(self, episodes: List[int], success_rates: List[float]) -> None:
        """更新成功率图表"""
        self.lines['success'].set_data(episodes, success_rates)
        
        ax = self.axes[0, 1]
        ax.relim()
        ax.autoscale_view()
    
    def _update_episode_length_plot(self, episodes: List[int], lengths: List[int]) -> None:
        """更新episode长度图表"""
        self.lines['length'].set_data(episodes, lengths)
        
        ax = self.axes[0, 2]
        ax.relim()
        ax.autoscale_view()
    
    def _update_loss_plot(self, episodes: List[int], losses: List[float]) -> None:
        """更新损失图表"""
        # 过滤掉0值（未提供损失数据的情况）
        valid_data = [(e, l) for e, l in zip(episodes, losses) if l > 0]
        if valid_data:
            valid_episodes, valid_losses = zip(*valid_data)
            self.lines['loss'].set_data(valid_episodes, valid_losses)
            
            ax = self.axes[1, 0]
            ax.relim()
            ax.autoscale_view()
    
    def _update_learning_rate_plot(self, episodes: List[int], lrs: List[float]) -> None:
        """更新学习率图表"""
        # 过滤掉0值
        valid_data = [(e, lr) for e, lr in zip(episodes, lrs) if lr > 0]
        if valid_data:
            valid_episodes, valid_lrs = zip(*valid_data)
            self.lines['lr'].set_data(valid_episodes, valid_lrs)
            
            ax = self.axes[1, 1]
            ax.relim()
            ax.autoscale_view()
    
    def _update_stage_progress_plot(self, episodes: List[int], stages: List[int]) -> None:
        """更新阶段进度图表"""
        ax = self.axes[1, 2]
        ax.clear()
        
        # 重新设置基本属性
        ax.set_title('课程学习进度', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('阶段')
        ax.grid(True, alpha=0.3)
        
        # 绘制阶段区域
        if episodes and stages:
            current_stage = stages[0]
            start_episode = episodes[0]
            
            for i in range(1, len(stages)):
                if stages[i] != current_stage:
                    # 绘制当前阶段区域
                    ax.axvspan(start_episode, episodes[i], alpha=0.3,
                              color=plt.cm.Set3(current_stage % 12),
                              label=f'阶段{current_stage}')
                    start_episode = episodes[i]
                    current_stage = stages[i]
            
            # 绘制最后一个阶段
            ax.axvspan(start_episode, episodes[-1], alpha=0.3,
                      color=plt.cm.Set3(current_stage % 12),
                      label=f'阶段{current_stage}')
            
            # 限制图例数量
            handles, labels = ax.get_legend_handles_labels()
            if len(handles) > 6:
                ax.legend(handles[-6:], labels[-6:], loc='upper left')
            else:
                ax.legend(loc='upper left')
    
    def _save_current_plot(self) -> None:
        """保存当前图表 - 已禁用以避免产生额外图片"""
        # 禁用自动保存功能，避免产生大量图片文件
        # 现在由TrainingMonitor和PerformanceTracker负责生成固定文件名的图片
        pass
    
    def close_plots(self) -> None:
        """关闭动态图表"""
        self.is_plotting = False

        try:
            if self.fig is not None:
                plt.close(self.fig)
                self.fig = None
                self.axes = None
                self.lines = {}
                print("📊 动态图表已关闭")
            else:
                print("📊 动态图表已关闭（无需清理）")
        except Exception as e:
            print(f"⚠️  关闭图表时出现警告: {e}")
            # 强制清理状态
            self.fig = None
            self.axes = None
            self.lines = {}
            print("📊 动态图表状态已重置")
    
    def save_final_plot(self) -> Optional[str]:
        """保存最终图表 - 已禁用以避免产生额外图片"""
        # 禁用最终图表保存功能，避免产生额外图片文件
        # 现在由TrainingMonitor和PerformanceTracker负责生成固定文件名的图片
        print("📊 DynamicPlotter最终图表保存已禁用（避免冗余图片）")
        return None
    
    def export_data(self) -> str:
        """导出训练数据"""
        data = {
            'episodes': list(self.data_buffer['episodes']),
            'rewards': list(self.data_buffer['rewards']),
            'success_rates': list(self.data_buffer['success_rates']),
            'episode_lengths': list(self.data_buffer['episode_lengths']),
            'stages': list(self.data_buffer['stages']),
            'loss_values': list(self.data_buffer['loss_values']),
            'learning_rates': list(self.data_buffer['learning_rates']),
            'metadata': {
                'update_interval': self.update_interval,
                'max_points': self.max_points,
                'exported_at': datetime.now().isoformat()
            }
        }
        
        data_path = os.path.join(self.save_dir, 'training_data_export.json')
        with open(data_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return data_path


if __name__ == "__main__":
    # 测试动态绘制器
    print("📈 测试动态绘制器...")
    
    plotter = DynamicPlotter(save_dir="results/test_dynamic_plotter", update_interval=10)
    plotter.initialize_plots()
    
    # 模拟训练数据
    np.random.seed(42)
    try:
        for episode in range(1, 101):
            stage = min((episode - 1) // 25 + 1, 4)
            
            # 模拟训练指标
            reward = stage * 10 + episode * 0.1 + np.random.normal(0, 5)
            success_rate = min(max(episode * 0.01 + np.random.normal(0, 0.1), 0), 1)
            episode_length = max(50 + np.random.normal(0, 10), 10)
            loss_value = max(10 - episode * 0.05 + np.random.normal(0, 1), 0.1)
            learning_rate = 0.001 * (0.99 ** (episode // 10))
            
            plotter.add_data_point(episode, reward, success_rate, 
                                 int(episode_length), stage, loss_value, learning_rate)
            
            # 模拟训练延迟
            time.sleep(0.1)
        
        # 保存最终图表和数据
        final_plot = plotter.save_final_plot()
        data_export = plotter.export_data()
        
        print(f"✅ 动态绘制器测试完成")
        print(f"  最终图表: {final_plot}")
        print(f"  数据导出: {data_export}")
        
    finally:
        plotter.close_plots()
