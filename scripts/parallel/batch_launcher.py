#!/usr/bin/env python3
"""
分批并行训练启动器
根据系统资源分批启动训练，避免内存不足
"""

import os
import sys
import subprocess
import time
import psutil
from datetime import datetime

def get_system_resources():
    """获取系统资源信息"""
    memory = psutil.virtual_memory()
    cpu_count = psutil.cpu_count()
    
    return {
        'total_memory_gb': memory.total / (1024**3),
        'available_memory_gb': memory.available / (1024**3),
        'memory_percent': memory.percent,
        'cpu_count': cpu_count
    }

def calculate_optimal_batch_size():
    """根据系统资源计算最优批次大小"""
    resources = get_system_resources()
    
    # 每个训练进程大约需要2-3GB内存
    memory_per_process = 2.5  # GB
    available_memory = resources['available_memory_gb']
    
    # 保留4GB内存给系统
    usable_memory = max(available_memory - 4, 4)
    max_processes = int(usable_memory / memory_per_process)
    
    # 限制最大并行数，避免过载
    optimal_batch = min(max_processes, 6)
    
    print(f"📊 系统资源分析:")
    print(f"  总内存: {resources['total_memory_gb']:.1f}GB")
    print(f"  可用内存: {available_memory:.1f}GB")
    print(f"  内存使用率: {resources['memory_percent']:.1f}%")
    print(f"  CPU核心数: {resources['cpu_count']}")
    print(f"  建议并行数: {optimal_batch}")
    
    return optimal_batch

def launch_batch_training(stages, batch_name=""):
    """启动一批训练"""
    print(f"\n🚀 启动{batch_name}训练批次")
    print(f"阶段: {stages}")
    print("-" * 50)
    
    # 阶段配置
    stage_configs = {
        1: (1, 1, 800), 2: (1, 2, 1000), 3: (1, 4, 1200), 4: (2, 4, 1200),
        5: (2, 6, 1300), 6: (2, 8, 1300), 7: (3, 8, 1400), 8: (3, 10, 1400),
        9: (3, 12, 1500), 10: (4, 12, 1600), 11: (4, 14, 1600), 12: (4, 16, 1600)
    }
    
    launched_processes = []
    
    for stage in stages:
        if stage not in stage_configs:
            continue
            
        agv_num, task_num, episodes = stage_configs[stage]
        complexity = agv_num * task_num
        
        # 生成日志文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = f"logs/stage_{stage:02d}_{timestamp}.log"
        
        # 构建命令
        script_path = f"scripts/parallel/train_stage_{stage:02d}.py"
        cmd = [sys.executable, script_path]
        
        print(f"启动阶段{stage:2d}: {agv_num}AGV, {task_num}任务, 复杂度{complexity}")
        
        try:
            # 在后台启动进程
            with open(log_file, 'w') as log_f:
                process = subprocess.Popen(
                    cmd,
                    stdout=log_f,
                    stderr=subprocess.STDOUT,
                    cwd=os.getcwd(),
                    env=os.environ.copy()
                )
            
            launched_processes.append({
                'stage': stage,
                'pid': process.pid,
                'process': process,
                'log_file': log_file
            })
            
            print(f"  ✅ PID: {process.pid}")
            
            # 等待3秒避免同时启动造成冲突
            time.sleep(3)
            
        except Exception as e:
            print(f"  ❌ 启动失败: {e}")
    
    return launched_processes

def monitor_batch_completion(processes, check_interval=60):
    """监控批次完成情况"""
    print(f"\n📊 监控训练进展 (每{check_interval}秒检查一次)")
    print("-" * 50)
    
    while processes:
        time.sleep(check_interval)
        
        completed = []
        for proc_info in processes:
            if proc_info['process'].poll() is not None:
                completed.append(proc_info)
        
        # 移除已完成的进程
        for proc_info in completed:
            processes.remove(proc_info)
            print(f"✅ 阶段{proc_info['stage']}训练完成 (PID: {proc_info['pid']})")
        
        if processes:
            print(f"🔄 还有{len(processes)}个阶段在训练中...")
            
            # 显示内存使用情况
            memory = psutil.virtual_memory()
            print(f"   内存使用: {memory.percent:.1f}% ({memory.used/(1024**3):.1f}GB/{memory.total/(1024**3):.1f}GB)")
        else:
            print("🎉 当前批次所有训练已完成！")
            break

def main():
    """主函数"""
    print("🏭 分批并行训练启动器")
    print("=" * 60)
    
    # 检查环境
    if os.environ.get('CONDA_DEFAULT_ENV') != 'biye_RL':
        print("❌ 请先激活biye_RL环境: conda activate biye_RL")
        return
    
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    
    # 计算最优批次大小
    optimal_batch = calculate_optimal_batch_size()
    
    # 定义训练批次
    if optimal_batch >= 6:
        # 系统资源充足，分两批
        batches = [
            ([1, 2, 3, 4, 5, 6], "第一批(阶段1-6)"),
            ([7, 8, 9, 10, 11, 12], "第二批(阶段7-12)")
        ]
    elif optimal_batch >= 4:
        # 系统资源中等，分三批
        batches = [
            ([1, 2, 3, 4], "第一批(阶段1-4)"),
            ([5, 6, 7, 8], "第二批(阶段5-8)"),
            ([9, 10, 11, 12], "第三批(阶段9-12)")
        ]
    else:
        # 系统资源有限，分四批
        batches = [
            ([1, 2, 3], "第一批(阶段1-3)"),
            ([4, 5, 6], "第二批(阶段4-6)"),
            ([7, 8, 9], "第三批(阶段7-9)"),
            ([10, 11, 12], "第四批(阶段10-12)")
        ]
    
    print(f"\n📋 训练计划: 分{len(batches)}批执行")
    for i, (stages, name) in enumerate(batches, 1):
        print(f"  批次{i}: {name} - 阶段{stages}")
    
    # 询问用户确认
    print()
    response = input("确认开始分批训练? (y/N): ")
    if response.lower() != 'y':
        print("❌ 取消训练")
        return
    
    # 执行分批训练
    total_start_time = time.time()
    
    for i, (stages, batch_name) in enumerate(batches, 1):
        print(f"\n{'='*60}")
        print(f"开始执行批次{i}/{len(batches)}: {batch_name}")
        print(f"{'='*60}")
        
        # 启动当前批次
        processes = launch_batch_training(stages, batch_name)
        
        if not processes:
            print(f"❌ 批次{i}启动失败")
            continue
        
        print(f"✅ 批次{i}已启动 ({len(processes)}个进程)")
        
        # 如果不是最后一批，等待当前批次完成
        if i < len(batches):
            print(f"⏳ 等待批次{i}完成后启动下一批...")
            monitor_batch_completion(processes)
        else:
            print(f"🎯 最后一批已启动，所有训练进程正在后台运行")
    
    total_time = time.time() - total_start_time
    print(f"\n🎉 所有批次启动完成！")
    print(f"⏱️ 总启动时间: {total_time/60:.1f}分钟")
    print(f"📁 日志目录: logs/")
    print(f"📊 监控命令: python scripts/parallel/monitor_training.py")

if __name__ == "__main__":
    main()
