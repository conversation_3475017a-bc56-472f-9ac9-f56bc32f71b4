#!/bin/bash
# 阶段6独立启动脚本
# 配置: 2个AGV, 8个任务, 复杂度16

echo "🚀 启动阶段6训练"
echo "配置: 2个AGV, 8个任务, 复杂度16"
echo "目标episodes: 1300"
echo ""

# 检查环境
if [[ "$CONDA_DEFAULT_ENV" != "biye_RL" ]]; then
    echo "❌ 请先激活biye_RL环境: conda activate biye_RL"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动训练
echo "开始训练..."
python scripts/parallel/train_stage_06.py 2>&1 | tee logs/stage_06_$(date +%Y%m%d_%H%M%S).log

echo "训练完成！"
