#!/usr/bin/env python3
"""
后台并行训练启动器
在后台同时启动12个阶段的训练
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def launch_background_training():
    """在后台启动所有12个阶段的训练"""
    print("🚀 启动12阶段后台并行训练")
    print("=" * 60)
    
    # 检查环境
    if os.environ.get('CONDA_DEFAULT_ENV') != 'biye_RL':
        print("❌ 请先激活biye_RL环境: conda activate biye_RL")
        return False
    
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    
    # 阶段配置
    stage_configs = {
        1: (1, 1, 800), 2: (1, 2, 1000), 3: (1, 4, 1200), 4: (2, 4, 1200),
        5: (2, 6, 1300), 6: (2, 8, 1300), 7: (3, 8, 1400), 8: (3, 10, 1400),
        9: (3, 12, 1500), 10: (4, 12, 1600), 11: (4, 14, 1600), 12: (4, 16, 1600)
    }
    
    launched_processes = []
    
    print("📋 启动训练进程:")
    print("-" * 60)
    
    for stage in range(1, 13):
        agv_num, task_num, episodes = stage_configs[stage]
        complexity = agv_num * task_num
        
        # 生成日志文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = f"logs/stage_{stage:02d}_{timestamp}.log"
        
        # 构建命令
        script_path = f"scripts/parallel/train_stage_{stage:02d}.py"
        cmd = [
            sys.executable,  # python
            script_path
        ]
        
        print(f"启动阶段{stage:2d}: {agv_num}AGV, {task_num}任务, 复杂度{complexity}, 目标{episodes}episodes")
        
        try:
            # 在后台启动进程
            with open(log_file, 'w') as log_f:
                process = subprocess.Popen(
                    cmd,
                    stdout=log_f,
                    stderr=subprocess.STDOUT,
                    cwd=os.getcwd(),
                    env=os.environ.copy()
                )
            
            launched_processes.append({
                'stage': stage,
                'pid': process.pid,
                'process': process,
                'log_file': log_file,
                'script_path': script_path
            })
            
            print(f"  ✅ PID: {process.pid}, 日志: {log_file}")
            
            # 等待2秒避免同时启动造成冲突
            time.sleep(2)
            
        except Exception as e:
            print(f"  ❌ 启动失败: {e}")
    
    print()
    print("🎉 所有训练进程已启动！")
    print("=" * 60)
    print(f"📊 成功启动: {len(launched_processes)}/12 个进程")
    
    # 保存进程信息
    process_info_file = f"logs/training_processes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(process_info_file, 'w', encoding='utf-8') as f:
        f.write(f"12阶段并行训练进程信息\n")
        f.write(f"启动时间: {datetime.now().isoformat()}\n")
        f.write(f"=" * 60 + "\n\n")
        
        for proc_info in launched_processes:
            f.write(f"阶段{proc_info['stage']:2d}: PID {proc_info['pid']}\n")
            f.write(f"  脚本: {proc_info['script_path']}\n")
            f.write(f"  日志: {proc_info['log_file']}\n\n")
    
    print(f"📄 进程信息已保存: {process_info_file}")
    print()
    print("💡 管理命令:")
    print("  查看所有进程: ps aux | grep train_stage")
    print("  监控训练: python scripts/parallel/monitor_training.py")
    print("  查看日志: tail -f logs/stage_XX_*.log")
    print("  停止所有: pkill -f train_stage")
    print()
    print("📈 预计完成时间: 约21小时")
    print("📁 结果目录: results/")
    
    return True

def check_running_processes():
    """检查当前运行的训练进程"""
    print("🔍 检查当前运行的训练进程")
    print("-" * 40)
    
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        training_processes = []
        for line in lines:
            if 'train_stage_' in line and 'python' in line:
                training_processes.append(line)
        
        if training_processes:
            print(f"📊 发现 {len(training_processes)} 个训练进程:")
            for i, process in enumerate(training_processes, 1):
                parts = process.split()
                if len(parts) >= 11:
                    pid = parts[1]
                    cpu = parts[2]
                    mem = parts[3]
                    cmd = ' '.join(parts[10:])
                    print(f"  {i}. PID:{pid} CPU:{cpu}% MEM:{mem}%")
                    print(f"     {cmd}")
        else:
            print("❌ 未发现训练进程")
        
        return len(training_processes)
        
    except Exception as e:
        print(f"❌ 检查进程时出错: {e}")
        return 0

def main():
    """主函数"""
    print("🏭 12阶段后台并行训练启动器")
    print("=" * 60)
    
    # 首先检查是否已有训练进程在运行
    running_count = check_running_processes()
    
    if running_count > 0:
        print()
        response = input("⚠️ 检测到已有训练进程在运行，是否继续启动新的训练? (y/N): ")
        if response.lower() != 'y':
            print("❌ 取消启动")
            return
    
    print()
    # 启动并行训练
    success = launch_background_training()
    
    if success:
        print("🎯 训练已在后台启动，您可以:")
        print("1. 关闭此终端，训练将继续运行")
        print("2. 使用监控脚本查看进展")
        print("3. 查看日志文件了解详细情况")
    else:
        print("❌ 启动失败，请检查环境配置")

if __name__ == "__main__":
    main()
