"""
地图生成器
负责生成26×10网格的仓储环境地图，包括货架布局和通道设计
"""

import numpy as np
import yaml
from typing import Tuple, List, Dict
import os

class MapGenerator:
    """仓储地图生成器"""
    
    def __init__(self, config_path: str = "configs/environment_config.yaml"):
        """
        初始化地图生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.map_config = self.config['map']
        
        # 地图尺寸
        self.width = self.map_config['width']
        self.height = self.map_config['height']
        
        # 货架配置
        self.shelf_rows = self.map_config['shelves']['rows']
        self.shelf_cols = self.map_config['shelves']['cols']
        self.shelf_width = self.map_config['shelves']['shelf_width']
        self.shelf_height = self.map_config['shelves']['shelf_height']
        
        # 通道宽度
        self.corridor_width = self.map_config['corridor_width']
        
        # 边界点
        self.start_point = tuple(self.map_config['start_point'])
        self.end_point = tuple(self.map_config['end_point'])
        
        # 地图元素编码
        self.EMPTY = 0      # 空地/通道
        self.WALL = 1       # 墙壁
        self.SHELF = 2      # 货架
        self.START = 3      # 起点
        self.END = 4        # 终点
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件未找到: {config_path}")
    
    def generate_map(self) -> np.ndarray:
        """
        生成完整的仓储地图
        
        Returns:
            np.ndarray: 地图矩阵 (height, width)
        """
        # 初始化空地图
        map_grid = np.zeros((self.height, self.width), dtype=np.int32)
        
        # 生成货架
        self._place_shelves(map_grid)
        
        # 设置边界点
        map_grid[self.start_point[1], self.start_point[0]] = self.START
        map_grid[self.end_point[1], self.end_point[0]] = self.END
        
        return map_grid
    
    def _place_shelves(self, map_grid: np.ndarray) -> None:
        """
        在地图上放置货架
        
        Args:
            map_grid: 地图矩阵
        """
        # 计算货架起始位置
        # 3行5列的货架布局，每个货架4×2，通道宽度为1
        
        shelf_id = 0
        for row in range(self.shelf_rows):
            for col in range(self.shelf_cols):
                # 计算当前货架的左上角位置
                start_x = col * (self.shelf_width + self.corridor_width) + self.corridor_width
                start_y = row * (self.shelf_height + self.corridor_width) + self.corridor_width
                
                # 检查边界
                if (start_x + self.shelf_width <= self.width and 
                    start_y + self.shelf_height <= self.height):
                    
                    # 放置货架
                    for y in range(start_y, start_y + self.shelf_height):
                        for x in range(start_x, start_x + self.shelf_width):
                            if 0 <= x < self.width and 0 <= y < self.height:
                                map_grid[y, x] = self.SHELF
                    
                    shelf_id += 1
    
    def get_valid_positions(self, map_grid: np.ndarray) -> List[Tuple[int, int]]:
        """
        获取地图上所有可通行的位置
        
        Args:
            map_grid: 地图矩阵
            
        Returns:
            List[Tuple[int, int]]: 可通行位置列表 [(x, y), ...]
        """
        valid_positions = []
        
        for y in range(self.height):
            for x in range(self.width):
                if map_grid[y, x] in [self.EMPTY, self.START, self.END]:
                    valid_positions.append((x, y))
        
        return valid_positions
    
    def get_shelf_adjacent_positions(self, map_grid: np.ndarray) -> List[Tuple[int, int]]:
        """
        获取货架邻近的可通行位置（用于任务生成）
        
        Args:
            map_grid: 地图矩阵
            
        Returns:
            List[Tuple[int, int]]: 货架邻近位置列表
        """
        adjacent_positions = []
        
        # 遍历所有货架位置
        for y in range(self.height):
            for x in range(self.width):
                if map_grid[y, x] == self.SHELF:
                    # 检查四个方向的邻近位置
                    for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
                        adj_x, adj_y = x + dx, y + dy
                        
                        # 检查边界和可通行性
                        if (0 <= adj_x < self.width and 
                            0 <= adj_y < self.height and
                            map_grid[adj_y, adj_x] in [self.EMPTY, self.START, self.END]):
                            
                            if (adj_x, adj_y) not in adjacent_positions:
                                adjacent_positions.append((adj_x, adj_y))
        
        return adjacent_positions
    
    def is_valid_position(self, map_grid: np.ndarray, x: int, y: int) -> bool:
        """
        检查位置是否可通行
        
        Args:
            map_grid: 地图矩阵
            x, y: 位置坐标
            
        Returns:
            bool: 是否可通行
        """
        if not (0 <= x < self.width and 0 <= y < self.height):
            return False
        
        return map_grid[y, x] in [self.EMPTY, self.START, self.END]
    
    def get_map_info(self) -> Dict:
        """
        获取地图信息
        
        Returns:
            Dict: 地图信息字典
        """
        return {
            'width': self.width,
            'height': self.height,
            'shelf_count': self.shelf_rows * self.shelf_cols,
            'start_point': self.start_point,
            'end_point': self.end_point,
            'elements': {
                'EMPTY': self.EMPTY,
                'WALL': self.WALL,
                'SHELF': self.SHELF,
                'START': self.START,
                'END': self.END
            }
        }

if __name__ == "__main__":
    # 测试地图生成器
    print("🗺️  测试地图生成器...")
    
    generator = MapGenerator()
    map_grid = generator.generate_map()
    
    print(f"地图尺寸: {map_grid.shape}")
    print(f"地图信息: {generator.get_map_info()}")
    
    valid_positions = generator.get_valid_positions(map_grid)
    shelf_adjacent = generator.get_shelf_adjacent_positions(map_grid)
    
    print(f"可通行位置数量: {len(valid_positions)}")
    print(f"货架邻近位置数量: {len(shelf_adjacent)}")
    
    print("✅ 地图生成器测试完成！")
