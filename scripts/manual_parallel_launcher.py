#!/usr/bin/env python3
"""
手动并行训练启动器
提供简单的命令来在不同终端中启动12个阶段的训练
"""

import os
import sys
import subprocess
from datetime import datetime

def print_launch_commands():
    """打印启动命令"""
    print("🚀 12阶段并行训练启动命令")
    print("=" * 80)
    print()
    print("💡 使用方法:")
    print("1. 打开12个终端窗口")
    print("2. 在每个终端中执行对应的命令")
    print("3. 每个阶段将在独立的Ray集群中运行")
    print()
    print("📋 启动命令列表:")
    print("-" * 80)
    
    # 阶段配置
    stage_configs = {
        1: (1, 1, 800), 2: (1, 2, 1000), 3: (1, 4, 1200), 4: (2, 4, 1200),
        5: (2, 6, 1300), 6: (2, 8, 1300), 7: (3, 8, 1400), 8: (3, 10, 1400),
        9: (3, 12, 1500), 10: (4, 12, 1600), 11: (4, 14, 1600), 12: (4, 16, 1600)
    }
    
    for stage in range(1, 13):
        agv_num, task_num, episodes = stage_configs[stage]
        complexity = agv_num * task_num
        
        print(f"# 终端{stage:2d} - 阶段{stage:2d} ({agv_num}AGV, {task_num}任务, 复杂度{complexity})")
        print(f"cd /home/<USER>/桌面/zhangchao/biye")
        print(f"conda activate biye_RL")
        print(f"python scripts/parallel/train_stage_{stage:02d}.py")
        print()

def create_batch_launcher():
    """创建批量启动脚本"""
    batch_script = """#!/bin/bash
# 12阶段并行训练批量启动脚本

echo "🚀 启动12阶段并行训练"
echo "=============================="

# 检查conda环境
if [[ "$CONDA_DEFAULT_ENV" != "biye_RL" ]]; then
    echo "❌ 请先激活biye_RL环境: conda activate biye_RL"
    exit 1
fi

# 检查GPU可用性
echo "🔧 GPU环境检查:"
python -c "import torch; print(f'GPU可用: {torch.cuda.is_available()}')"

echo ""
echo "📋 将启动12个训练进程..."
echo ""

# 创建日志目录
mkdir -p logs

# 启动所有训练脚本
for stage in {01..12}; do
    echo "启动阶段${stage}训练..."
    
    # 使用gnome-terminal在新窗口中启动
    gnome-terminal --title="阶段${stage}训练" -- bash -c "
        cd $(pwd) && 
        conda activate biye_RL && 
        python scripts/parallel/train_stage_${stage}.py 2>&1 | tee logs/stage_${stage}_$(date +%Y%m%d_%H%M%S).log
        echo '训练完成，按Enter关闭...'
        read
    " &
    
    # 等待2秒避免同时启动造成冲突
    sleep 2
done

echo ""
echo "✅ 所有训练进程已启动"
echo "💡 提示:"
echo "  - 每个阶段在独立终端中运行"
echo "  - 日志保存在logs/目录中"
echo "  - 结果保存在results/目录中"
echo "  - 使用 'ps aux | grep python' 查看运行状态"
echo "  - 使用 'pkill -f train_stage' 停止所有训练"
"""
    
    batch_path = "scripts/parallel/batch_launch.sh"
    with open(batch_path, 'w', encoding='utf-8') as f:
        f.write(batch_script)
    
    os.chmod(batch_path, 0o755)
    return batch_path

def create_individual_launchers():
    """创建单独的启动脚本"""
    launchers = []
    
    # 阶段配置
    stage_configs = {
        1: (1, 1, 800), 2: (1, 2, 1000), 3: (1, 4, 1200), 4: (2, 4, 1200),
        5: (2, 6, 1300), 6: (2, 8, 1300), 7: (3, 8, 1400), 8: (3, 10, 1400),
        9: (3, 12, 1500), 10: (4, 12, 1600), 11: (4, 14, 1600), 12: (4, 16, 1600)
    }
    
    for stage in range(1, 13):
        agv_num, task_num, episodes = stage_configs[stage]
        complexity = agv_num * task_num
        
        launcher_script = f"""#!/bin/bash
# 阶段{stage}独立启动脚本
# 配置: {agv_num}个AGV, {task_num}个任务, 复杂度{complexity}

echo "🚀 启动阶段{stage}训练"
echo "配置: {agv_num}个AGV, {task_num}个任务, 复杂度{complexity}"
echo "目标episodes: {episodes}"
echo ""

# 检查环境
if [[ "$CONDA_DEFAULT_ENV" != "biye_RL" ]]; then
    echo "❌ 请先激活biye_RL环境: conda activate biye_RL"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动训练
echo "开始训练..."
python scripts/parallel/train_stage_{stage:02d}.py 2>&1 | tee logs/stage_{stage:02d}_$(date +%Y%m%d_%H%M%S).log

echo "训练完成！"
"""
        
        launcher_path = f"scripts/parallel/launch_stage_{stage:02d}.sh"
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_script)
        
        os.chmod(launcher_path, 0o755)
        launchers.append(launcher_path)
    
    return launchers

def create_monitoring_script():
    """创建监控脚本"""
    monitor_script = """#!/usr/bin/env python3
# 训练进程监控脚本

import os
import time
import subprocess
from datetime import datetime

def check_training_processes():
    \"\"\"检查训练进程状态\"\"\"
    print(f"🔍 训练进程监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 检查Python训练进程
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\\n')
        
        training_processes = []
        for line in lines:
            if 'train_stage_' in line and 'python' in line:
                training_processes.append(line)
        
        if training_processes:
            print(f"📊 发现 {len(training_processes)} 个训练进程:")
            for i, process in enumerate(training_processes, 1):
                parts = process.split()
                if len(parts) >= 11:
                    pid = parts[1]
                    cpu = parts[2]
                    mem = parts[3]
                    cmd = ' '.join(parts[10:])
                    print(f"  {i}. PID:{pid} CPU:{cpu}% MEM:{mem}% - {cmd}")
        else:
            print("❌ 未发现训练进程")
        
        print()
        
        # 检查GPU使用情况
        try:
            gpu_result = subprocess.run(['nvidia-smi', '--query-gpu=index,name,memory.used,memory.total,utilization.gpu', '--format=csv,noheader,nounits'], capture_output=True, text=True)
            if gpu_result.returncode == 0:
                print("🎮 GPU使用情况:")
                gpu_lines = gpu_result.stdout.strip().split('\\n')
                for line in gpu_lines:
                    parts = line.split(', ')
                    if len(parts) >= 5:
                        gpu_id, name, mem_used, mem_total, util = parts
                        print(f"  GPU{gpu_id}: {name} - 内存:{mem_used}MB/{mem_total}MB 利用率:{util}%")
            else:
                print("⚠️ 无法获取GPU信息")
        except FileNotFoundError:
            print("⚠️ nvidia-smi未找到")
        
        print()
        
    except Exception as e:
        print(f"❌ 监控出错: {e}")

def main():
    \"\"\"主监控循环\"\"\"
    print("🚀 启动训练监控")
    print("按Ctrl+C停止监控")
    print()
    
    try:
        while True:
            check_training_processes()
            print("等待30秒后刷新...")
            print("-" * 60)
            time.sleep(30)
    except KeyboardInterrupt:
        print("\\n👋 监控已停止")

if __name__ == "__main__":
    main()
"""
    
    monitor_path = "scripts/parallel/monitor_training.py"
    with open(monitor_path, 'w', encoding='utf-8') as f:
        f.write(monitor_script)
    
    os.chmod(monitor_path, 0o755)
    return monitor_path

def main():
    """主函数"""
    print("🏭 手动并行训练启动器")
    print("=" * 60)
    
    # 创建必要目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("scripts/parallel", exist_ok=True)
    
    # 打印启动命令
    print_launch_commands()
    
    # 创建批量启动脚本
    batch_launcher = create_batch_launcher()
    print(f"📄 批量启动脚本: {batch_launcher}")
    
    # 创建单独启动脚本
    individual_launchers = create_individual_launchers()
    print(f"📁 单独启动脚本: {len(individual_launchers)} 个")
    
    # 创建监控脚本
    monitor_script = create_monitoring_script()
    print(f"📊 监控脚本: {monitor_script}")
    
    print()
    print("🎉 启动器创建完成！")
    print("=" * 60)
    print("💡 使用方法:")
    print("1. 自动启动所有: bash scripts/parallel/batch_launch.sh")
    print("2. 手动启动单个: bash scripts/parallel/launch_stage_XX.sh")
    print("3. 监控训练进程: python scripts/parallel/monitor_training.py")
    print()
    print("⚠️ 注意事项:")
    print("- 确保在biye_RL环境中运行")
    print("- 每个阶段使用独立的Ray集群")
    print("- 建议监控GPU内存使用情况")
    print("- 可以随时停止单个阶段的训练")

if __name__ == "__main__":
    main()
