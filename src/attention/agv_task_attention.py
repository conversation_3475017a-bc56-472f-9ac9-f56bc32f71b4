"""
第一层注意力机制：AGV-任务匹配注意力
实现AGV与任务之间的注意力计算，考虑载重能力、距离和任务状态约束
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import yaml
from typing import Dict, Tuple, Optional
import math

class MultiHeadAttention(nn.Module):
    """多头注意力机制"""
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        """
        初始化多头注意力
        
        Args:
            d_model: 模型维度
            num_heads: 注意力头数
            dropout: Dropout率
        """
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            query: 查询张量 [batch_size, seq_len_q, d_model]
            key: 键张量 [batch_size, seq_len_k, d_model]
            value: 值张量 [batch_size, seq_len_v, d_model]
            mask: 掩码张量 [batch_size, seq_len_q, seq_len_k]
            
        Returns:
            output: 输出张量 [batch_size, seq_len_q, d_model]
            attention_weights: 注意力权重 [batch_size, num_heads, seq_len_q, seq_len_k]
        """
        batch_size, seq_len_q, _ = query.size()
        seq_len_k = key.size(1)
        
        # 残差连接的输入
        residual = query
        
        # 线性变换
        Q = self.w_q(query).view(batch_size, seq_len_q, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, seq_len_k, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, seq_len_k, self.num_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力（传递温度参数）
        attention_output, attention_weights = self._scaled_dot_product_attention(Q, K, V, mask, temperature=1.0)
        
        # 拼接多头
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len_q, self.d_model)
        
        # 输出投影
        output = self.w_o(attention_output)
        output = self.dropout(output)
        
        # 残差连接和层归一化
        output = self.layer_norm(output + residual)
        
        return output, attention_weights
    
    def _scaled_dot_product_attention(self, Q: torch.Tensor, K: torch.Tensor, V: torch.Tensor,
                                    mask: Optional[torch.Tensor] = None, temperature: float = 1.0) -> Tuple[torch.Tensor, torch.Tensor]:
        """缩放点积注意力（带温度调节）"""
        d_k = Q.size(-1)
        scores = torch.matmul(Q, K.transpose(-2, -1)) / (math.sqrt(d_k) * temperature)
        
        if mask is not None:
            # 扩展mask维度以匹配多头注意力
            if mask.dim() == 3:  # [batch_size, seq_len_q, seq_len_k]
                mask = mask.unsqueeze(1)  # [batch_size, 1, seq_len_q, seq_len_k]
            # 将mask为0的位置设置为很小的值
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)

        # 如果有掩码，将被掩码的位置的注意力权重设为0
        if mask is not None:
            if mask.dim() == 3:
                mask = mask.unsqueeze(1)
            attention_weights = attention_weights * mask
            # 重新归一化
            attention_sum = attention_weights.sum(dim=-1, keepdim=True)
            attention_weights = attention_weights / (attention_sum + 1e-8)

        attention_weights = self.dropout(attention_weights)

        output = torch.matmul(attention_weights, V)
        return output, attention_weights

class AGVTaskAttention(nn.Module):
    """AGV-任务匹配注意力层"""
    
    def __init__(self, config_path: str = "configs/attention_config.yaml"):
        """
        初始化AGV-任务注意力
        
        Args:
            config_path: 配置文件路径
        """
        super().__init__()
        
        # 加载配置
        self.config = self._load_config(config_path)
        self.layer1_config = self.config['layer1_agv_task_attention']
        self.attention_config = self.config['attention']
        
        # 网络参数
        self.hidden_dim = self.attention_config['dimensions']['hidden_dim']
        self.attention_dim = self.attention_config['dimensions']['attention_dim']
        self.num_heads = self.layer1_config['architecture']['attention_heads']
        self.dropout_rate = self.layer1_config['architecture']['dropout_rate']

        # 注意力参数
        self.temperature = self.layer1_config['attention_params']['temperature']
        self.use_layer_norm = self.layer1_config['attention_params']['use_layer_norm']
        self.use_residual = self.layer1_config['attention_params']['use_residual']
        
        # AGV特征编码器
        agv_layers = self.layer1_config['architecture']['agv_encoder_layers']
        self.agv_encoder = self._build_mlp([5] + agv_layers)
        
        # 任务特征编码器
        task_layers = self.layer1_config['architecture']['task_encoder_layers']
        self.task_encoder = self._build_mlp([4] + task_layers)
        
        # 多头注意力
        self.attention = MultiHeadAttention(
            d_model=self.hidden_dim,
            num_heads=self.num_heads,
            dropout=self.dropout_rate
        )
        
        # 输出投影
        self.output_projection = nn.Linear(self.hidden_dim, self.attention_config['dimensions']['output_dim'])
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _build_mlp(self, layer_sizes: list) -> nn.Module:
        """构建MLP网络"""
        layers = []
        for i in range(len(layer_sizes) - 1):
            layers.append(nn.Linear(layer_sizes[i], layer_sizes[i + 1]))
            if i < len(layer_sizes) - 2:  # 最后一层不加激活函数
                layers.append(nn.ReLU())
                layers.append(nn.Dropout(self.dropout_rate))
        return nn.Sequential(*layers)
    
    def forward(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                agv_positions: torch.Tensor, task_positions: torch.Tensor,
                agv_loads: torch.Tensor, agv_capacities: torch.Tensor,
                task_weights: torch.Tensor, task_states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_features: AGV特征 [batch_size, num_agvs, 5]
            task_features: 任务特征 [batch_size, num_tasks, 4]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            task_positions: 任务位置 [batch_size, num_tasks, 2]
            agv_loads: AGV当前载重 [batch_size, num_agvs]
            agv_capacities: AGV载重能力 [batch_size, num_agvs]
            task_weights: 任务重量 [batch_size, num_tasks]
            task_states: 任务状态 [batch_size, num_tasks]
            
        Returns:
            attention_output: 注意力输出 [batch_size, num_agvs, output_dim]
            attention_weights: 注意力权重 [batch_size, num_heads, num_agvs, num_tasks]
        """
        # 编码AGV和任务特征
        agv_encoded = self.agv_encoder(agv_features)  # [batch_size, num_agvs, hidden_dim]
        task_encoded = self.task_encoder(task_features)  # [batch_size, num_tasks, hidden_dim]
        
        # 生成掩码
        mask = self._generate_mask(
            agv_positions, task_positions, agv_loads, agv_capacities,
            task_weights, task_states
        )
        
        # 计算注意力
        attention_output, attention_weights = self.attention(
            query=agv_encoded,
            key=task_encoded,
            value=task_encoded,
            mask=mask
        )
        
        # 输出投影
        output = self.output_projection(attention_output)
        
        return output, attention_weights
    
    def _generate_mask(self, agv_positions: torch.Tensor, task_positions: torch.Tensor,
                      agv_loads: torch.Tensor, agv_capacities: torch.Tensor,
                      task_weights: torch.Tensor, task_states: torch.Tensor) -> torch.Tensor:
        """
        生成注意力掩码
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            task_positions: 任务位置 [batch_size, num_tasks, 2]
            agv_loads: AGV当前载重 [batch_size, num_agvs]
            agv_capacities: AGV载重能力 [batch_size, num_agvs]
            task_weights: 任务重量 [batch_size, num_tasks]
            task_states: 任务状态 [batch_size, num_tasks]
            
        Returns:
            mask: 掩码张量 [batch_size, num_agvs, num_tasks]
        """
        batch_size, num_agvs, _ = agv_positions.size()
        num_tasks = task_positions.size(1)
        
        # 初始化掩码（全为1，表示可以注意）
        mask = torch.ones(batch_size, num_agvs, num_tasks, device=agv_positions.device)
        
        masking_config = self.layer1_config['masking']
        
        # 1. 任务状态掩码：只关注未完成的任务
        if masking_config['enable_task_state_mask']:
            # task_states: 0=未分配, 1=已分配, 2=已完成
            task_available = (task_states == 0).float()  # [batch_size, num_tasks]
            task_available = task_available.unsqueeze(1).expand(-1, num_agvs, -1)
            mask = mask * task_available
        
        # 2. 载重能力掩码：AGV不能关注超出其载重能力的任务
        if masking_config['enable_capacity_mask']:
            # 计算剩余载重能力
            remaining_capacity = agv_capacities - agv_loads  # [batch_size, num_agvs]
            remaining_capacity = remaining_capacity.unsqueeze(2)  # [batch_size, num_agvs, 1]
            task_weights_expanded = task_weights.unsqueeze(1)  # [batch_size, 1, num_tasks]
            
            # 检查是否能承载任务重量
            capacity_mask = (remaining_capacity >= task_weights_expanded).float()
            mask = mask * capacity_mask
        
        # 3. 距离掩码：AGV只关注一定距离内的任务
        if masking_config['enable_distance_mask']:
            max_distance = masking_config['max_distance_threshold']

            # 计算AGV和任务之间的曼哈顿距离
            agv_pos_expanded = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
            task_pos_expanded = task_positions.unsqueeze(1)  # [batch_size, 1, num_tasks, 2]

            distances = torch.sum(torch.abs(agv_pos_expanded - task_pos_expanded), dim=-1)
            distance_mask = (distances <= max_distance).float()
            mask = mask * distance_mask

        # 4. 处理全零掩码的情况（鲁棒性改进）
        mask_sums = mask.sum(dim=-1, keepdim=True)  # [batch_size, num_agvs, 1]
        # 如果某个AGV的掩码全为0，则允许其关注最近的任务
        zero_mask_agvs = (mask_sums == 0).squeeze(-1)  # [batch_size, num_agvs]

        if zero_mask_agvs.any():
            # 为全零掩码的AGV找到最近的任务
            agv_pos_expanded = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
            task_pos_expanded = task_positions.unsqueeze(1)  # [batch_size, 1, num_tasks, 2]
            distances = torch.sum(torch.abs(agv_pos_expanded - task_pos_expanded), dim=-1)

            # 对于全零掩码的AGV，允许其关注最近的任务
            for batch_idx in range(batch_size):
                for agv_idx in range(num_agvs):
                    if zero_mask_agvs[batch_idx, agv_idx]:
                        # 找到最近的可用任务
                        agv_distances = distances[batch_idx, agv_idx]
                        available_tasks = (task_states[batch_idx] == 0)  # 未完成的任务

                        if available_tasks.any():
                            # 在可用任务中找最近的
                            available_distances = agv_distances.clone()
                            available_distances[~available_tasks] = float('inf')
                            nearest_task = torch.argmin(available_distances)
                            mask[batch_idx, agv_idx, nearest_task] = 1.0

        return mask
