#!/usr/bin/env python3
"""
MAPPO算法配置类
配置MAPPO算法的超参数和网络架构
"""

import yaml
from typing import Dict, Any, Optional
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.policy.policy import PolicySpec
import sys
import os
import ray
from packaging import version

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.mappo.model_registry import get_model_config
from src.mappo.multi_agent_env_wrapper import create_multi_agent_env


class MAPPOConfig:
    """
    MAPPO算法配置管理器

    负责配置MAPPO算法的所有参数，包括：
    - 算法超参数
    - 网络架构配置
    - 多智能体策略配置
    - 训练环境配置
    """

    def __init__(self, config_path: str = "configs/mappo_config.yaml"):
        """
        初始化MAPPO配置

        Args:
            config_path: MAPPO配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()

        # 基础配置
        self.algorithm_config = self.config.get('algorithm', {})
        self.training_config = self.config.get('training', {})
        self.environment_config = self.config.get('environment', {})
        self.model_config = self.config.get('model', {})

        # Ray版本检测
        self.ray_version = self._get_ray_version()
        self.api_config = self._get_api_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"⚠️  配置文件未找到: {self.config_path}，使用默认配置")
            return self._get_default_config()

    def _get_ray_version(self) -> str:
        """获取Ray版本"""
        try:
            import ray
            return ray.__version__
        except ImportError:
            return "unknown"

    def _get_api_config(self) -> Dict[str, Any]:
        """
        根据Ray版本获取API配置映射

        Returns:
            dict: API配置映射
        """
        # 检测Ray版本并返回对应的API配置
        ray_version_str = self.ray_version

        # 定义API配置映射
        api_config = {
            # 训练参数映射
            'training_params': {
                'minibatch_size_param': 'minibatch_size',  # 统一使用minibatch_size
                'epochs_param': 'num_epochs',  # 统一使用num_epochs
            },
            # 采样配置映射
            'sampling_method': 'env_runners',  # 统一使用env_runners
            'sampling_params': {
                'num_workers_param': 'num_env_runners',
                'num_envs_param': 'num_envs_per_env_runner',
            },
            # 评估配置映射
            'evaluation_params': {
                'num_workers_param': 'evaluation_num_env_runners',
            },
            # GPU配置
            'gpu_config': {
                'use_learners': True,  # 使用新的learners配置
                'learner_gpu_param': 'num_gpus_per_learner',
            }
        }

        return api_config

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'algorithm': {
                'learning_rate': 0.0003,
                'gamma': 0.99,
                'lambda': 0.95,
                'clip_param': 0.2,
                'vf_clip_param': 10.0,
                'entropy_coeff': 0.01,
                'vf_loss_coeff': 0.5,
                'kl_coeff': 0.2,
                'kl_target': 0.01,
                'grad_clip': 0.5,
                'use_gae': True,
                'use_critic': True
            },
            'training': {
                'train_batch_size': 4000,
                'sgd_minibatch_size': 128,
                'num_sgd_iter': 10,
                'rollout_fragment_length': 200,
                'batch_mode': 'truncate_episodes',
                'num_workers': 2,
                'num_envs_per_worker': 1,
                'evaluation_interval': 10,
                'evaluation_duration': 10,
                'evaluation_num_workers': 1
            },
            'environment': {
                'curriculum_stage': 1,
                'env_config_path': 'configs/environment_config.yaml',
                'render_mode': None
            },
            'model': {
                'max_agvs': 4,
                'max_tasks': 16,
                'attention_config_path': 'configs/attention_config.yaml'
            }
        }

    def create_ppo_config(self, curriculum_stage: int = 1) -> PPOConfig:
        """
        创建PPO配置对象

        Args:
            curriculum_stage: 课程学习阶段

        Returns:
            PPOConfig: 配置好的PPO配置对象
        """
        # 创建基础PPO配置
        config = PPOConfig()

        # 算法参数配置
        algo_config = self.algorithm_config
        config = config.training(
            lr=algo_config.get('learning_rate', 0.0003),
            gamma=algo_config.get('gamma', 0.99),
            lambda_=algo_config.get('lambda', 0.95),
            clip_param=algo_config.get('clip_param', 0.2),
            vf_clip_param=algo_config.get('vf_clip_param', 10.0),
            entropy_coeff=algo_config.get('entropy_coeff', 0.01),
            vf_loss_coeff=algo_config.get('vf_loss_coeff', 0.5),
            kl_coeff=algo_config.get('kl_coeff', 0.2),
            kl_target=algo_config.get('kl_target', 0.01),
            grad_clip=algo_config.get('grad_clip', 0.5),
            use_gae=algo_config.get('use_gae', True),
            use_critic=algo_config.get('use_critic', True)
        )

        # 训练参数配置 (使用统一API)
        train_config = self.training_config
        api_config = self.api_config

        # 统一的训练参数
        training_params = {
            'train_batch_size': train_config.get('train_batch_size', 4000),
            api_config['training_params']['minibatch_size_param']: train_config.get('sgd_minibatch_size', 128),
            api_config['training_params']['epochs_param']: train_config.get('num_sgd_iter', 10)
        }

        config = config.training(**training_params)

        # 采样配置 (使用统一API)
        sampling_params = {
            'rollout_fragment_length': train_config.get('rollout_fragment_length', 100),
            'batch_mode': train_config.get('batch_mode', 'truncate_episodes'),
            api_config['sampling_params']['num_workers_param']: train_config.get('num_workers', 0),
            api_config['sampling_params']['num_envs_param']: train_config.get('num_envs_per_worker', 1)
        }

        # 使用统一的采样方法
        if api_config['sampling_method'] == 'env_runners':
            config = config.env_runners(**sampling_params)
        else:
            # 兼容旧版本API
            config = config.rollouts(**sampling_params)

        # 评估配置 (使用统一API)
        eval_workers = train_config.get('evaluation_num_workers', 0)
        eval_params = {
            'evaluation_interval': train_config.get('evaluation_interval', 20),
            'evaluation_duration': train_config.get('evaluation_duration', 5),
            api_config['evaluation_params']['num_workers_param']: eval_workers
        }

        config = config.evaluation(**eval_params)

        # 环境配置
        env_config = self.environment_config.copy()
        env_config['curriculum_stage'] = curriculum_stage

        config = config.environment(
            env="multi_agent_agv_env",
            env_config=env_config,
            disable_env_checking=True
        )

        # 框架配置
        config = config.framework("torch")

        # GPU配置 (使用统一API)
        gpu_config = api_config['gpu_config']
        num_gpus = train_config.get('num_gpus', 1)

        # 基础GPU资源配置
        config = config.resources(num_gpus=num_gpus)

        # 学习器GPU配置 (新API栈)
        if gpu_config['use_learners'] and num_gpus > 0:
            learner_params = {
                'num_learners': 1,
                gpu_config['learner_gpu_param']: num_gpus
            }
            config = config.learners(**learner_params)

        return config

    def validate_config_consistency(self, config: PPOConfig) -> Dict[str, Any]:
        """
        验证配置一致性，返回实际使用的参数

        Args:
            config: PPO配置对象

        Returns:
            dict: 实际配置参数
        """
        actual_config = {
            'ray_version': self.ray_version,
            'train_batch_size': getattr(config, 'train_batch_size', 'unknown'),
            'minibatch_size': getattr(config, 'minibatch_size', getattr(config, 'sgd_minibatch_size', 'unknown')),
            'num_epochs': getattr(config, 'num_epochs', getattr(config, 'num_sgd_iter', 'unknown')),
            'num_gpus': getattr(config, 'num_gpus', 'unknown'),
            'learning_rate': getattr(config, 'lr', 'unknown'),
            'gamma': getattr(config, 'gamma', 'unknown'),
        }

        return actual_config

    def create_multi_agent_config(self, curriculum_stage: int = 1) -> PPOConfig:
        """
        创建多智能体PPO配置

        Args:
            curriculum_stage: 课程学习阶段

        Returns:
            PPOConfig: 配置好的多智能体PPO配置
        """
        # 获取基础配置
        config = self.create_ppo_config(curriculum_stage)

        # 获取模型配置
        model_config = get_model_config(
            max_agvs=self.model_config.get('max_agvs', 4),
            max_tasks=self.model_config.get('max_tasks', 16),
            attention_config_path=self.model_config.get('attention_config_path', 'configs/attention_config.yaml')
        )

        # 获取环境空间信息
        from gymnasium.spaces import Box, Discrete
        import numpy as np

        # 计算观察空间维度
        max_agvs = self.model_config.get('max_agvs', 4)
        max_tasks = self.model_config.get('max_tasks', 16)
        obs_dim = max_tasks * 4 + max_agvs * 5  # 任务4维 + AGV5维

        # 定义观察空间和动作空间
        observation_space = Box(
            low=-np.inf,
            high=np.inf,
            shape=(obs_dim,),
            dtype=np.float32
        )
        action_space = Discrete(5)  # UP, DOWN, LEFT, RIGHT, WAIT

        # 多智能体策略配置
        policies = {
            "shared_policy": PolicySpec(
                policy_class=None,  # 使用默认策略类
                observation_space=observation_space,
                action_space=action_space,
                config={"model": model_config}
            )
        }

        # 策略映射函数：所有AGV使用相同策略
        def policy_mapping_fn(agent_id, episode=None, **kwargs):
            return "shared_policy"

        # 配置多智能体设置
        config = config.multi_agent(
            policies=policies,
            policy_mapping_fn=policy_mapping_fn,
            policies_to_train=["shared_policy"]
        )

        return config

    def get_curriculum_stages(self) -> Dict[int, Dict[str, Any]]:
        """
        获取课程学习阶段配置

        Returns:
            dict: 课程学习阶段配置字典
        """
        # 从环境配置文件加载课程学习配置
        env_config_path = self.environment_config.get('env_config_path', 'configs/environment_config.yaml')

        try:
            with open(env_config_path, 'r', encoding='utf-8') as f:
                env_config = yaml.safe_load(f)

            curriculum_stages = {}
            for stage_config in env_config.get('curriculum', {}).get('stages', []):
                stage_num = stage_config['stage']
                curriculum_stages[stage_num] = stage_config

            return curriculum_stages

        except FileNotFoundError:
            print(f"⚠️  环境配置文件未找到: {env_config_path}")
            return self._get_default_curriculum_stages()

    def _get_default_curriculum_stages(self) -> Dict[int, Dict[str, Any]]:
        """获取默认课程学习阶段"""
        return {
            1: {"stage": 1, "agv_num": 1, "task_num": 1, "max_steps": 500, "episodes": 800},
            2: {"stage": 2, "agv_num": 1, "task_num": 2, "max_steps": 800, "episodes": 1000},
            3: {"stage": 3, "agv_num": 1, "task_num": 4, "max_steps": 1200, "episodes": 1200},
            4: {"stage": 4, "agv_num": 2, "task_num": 4, "max_steps": 1500, "episodes": 1200},
            5: {"stage": 5, "agv_num": 2, "task_num": 6, "max_steps": 2000, "episodes": 1300},
            6: {"stage": 6, "agv_num": 2, "task_num": 8, "max_steps": 2500, "episodes": 1300},
            7: {"stage": 7, "agv_num": 3, "task_num": 8, "max_steps": 3000, "episodes": 1400},
            8: {"stage": 8, "agv_num": 3, "task_num": 10, "max_steps": 3500, "episodes": 1400},
            9: {"stage": 9, "agv_num": 3, "task_num": 12, "max_steps": 4000, "episodes": 1500},
            10: {"stage": 10, "agv_num": 4, "task_num": 12, "max_steps": 4500, "episodes": 1600},
            11: {"stage": 11, "agv_num": 4, "task_num": 14, "max_steps": 5000, "episodes": 1600},
            12: {"stage": 12, "agv_num": 4, "task_num": 16, "max_steps": 6000, "episodes": 1700}
        }

    def get_stage_config(self, stage: int) -> Dict[str, Any]:
        """
        获取指定阶段的配置

        Args:
            stage: 课程学习阶段

        Returns:
            dict: 阶段配置
        """
        stages = self.get_curriculum_stages()
        return stages.get(stage, stages[1])  # 默认返回第1阶段

    def save_config(self, save_path: str):
        """
        保存当前配置到文件

        Args:
            save_path: 保存路径
        """
        with open(save_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
        print(f"✅ 配置已保存到: {save_path}")


if __name__ == "__main__":
    # 测试MAPPO配置
    print("⚙️  测试MAPPO配置...")

    # 创建配置管理器
    mappo_config = MAPPOConfig()

    print(f"✅ 配置管理器创建成功")
    print(f"  算法配置: {len(mappo_config.algorithm_config)} 项")
    print(f"  训练配置: {len(mappo_config.training_config)} 项")
    print(f"  环境配置: {len(mappo_config.environment_config)} 项")
    print(f"  模型配置: {len(mappo_config.model_config)} 项")

    # 测试PPO配置创建
    ppo_config = mappo_config.create_ppo_config(curriculum_stage=4)
    print(f"\n🧠 PPO配置创建成功")
    print(f"  学习率: {ppo_config.lr}")
    print(f"  折扣因子: {ppo_config.gamma}")
    print(f"  训练批大小: {ppo_config.train_batch_size}")

    # 测试多智能体配置创建
    multi_agent_config = mappo_config.create_multi_agent_config(curriculum_stage=4)
    print(f"\n🤖 多智能体配置创建成功")
    print(f"  策略数量: {len(multi_agent_config.policies) if multi_agent_config.policies else 0}")

    # 测试课程学习配置
    curriculum_stages = mappo_config.get_curriculum_stages()
    print(f"\n📚 课程学习配置:")
    print(f"  总阶段数: {len(curriculum_stages)}")
    for stage, config in list(curriculum_stages.items())[:3]:  # 显示前3个阶段
        print(f"  阶段{stage}: {config['agv_num']}个AGV, {config['task_num']}个任务")

    # 测试阶段配置获取
    stage_4_config = mappo_config.get_stage_config(4)
    print(f"\n📋 阶段4配置: {stage_4_config}")

    print("\n🎉 MAPPO配置测试完成！")