#!/bin/bash
# 停止所有训练进程的脚本

TIMESTAMP=$1

if [ -z "$TIMESTAMP" ]; then
    echo "❌ 请提供时间戳参数"
    echo "用法: bash stop_all_training.sh TIMESTAMP"
    echo "或者: pkill -f train_stage (强制停止所有)"
    exit 1
fi

PID_DIR="/tmp/training_pids_${TIMESTAMP}"

if [ ! -d "$PID_DIR" ]; then
    echo "❌ PID目录不存在: $PID_DIR"
    echo "尝试强制停止所有训练进程..."
    pkill -f train_stage
    exit 1
fi

echo "🛑 停止训练会话: $TIMESTAMP"
echo "=============================="

# 逐个停止训练进程
for stage in {01..12}; do
    if [ -f "$PID_DIR/stage_${stage}.pid" ]; then
        PID=$(cat "$PID_DIR/stage_${stage}.pid")
        if ps -p $PID > /dev/null 2>&1; then
            echo "停止阶段${stage} (PID: $PID)..."
            kill $PID
            sleep 1
            if ps -p $PID > /dev/null 2>&1; then
                echo "  强制停止阶段${stage}..."
                kill -9 $PID
            fi
            echo "  ✅ 阶段${stage}已停止"
        else
            echo "  ⚠️ 阶段${stage}进程不存在 (PID: $PID)"
        fi
    else
        echo "  ⚠️ 阶段${stage} PID文件不存在"
    fi
done

echo ""
echo "🧹 清理资源..."
rm -rf "$PID_DIR"
echo "✅ 所有训练进程已停止"
