#!/bin/bash
# 改进的12阶段并行训练后台启动脚本
# 解决gnome-terminal依赖问题，使用后台进程启动

echo "🚀 启动12阶段并行训练 (后台模式)"
echo "=============================="

# 检查conda环境
if [[ "$CONDA_DEFAULT_ENV" != "biye_RL" ]]; then
    echo "❌ 请先激活biye_RL环境: conda activate biye_RL"
    exit 1
fi

# 检查GPU可用性
echo "🔧 GPU环境检查:"
python -c "import torch; print(f'GPU可用: {torch.cuda.is_available()}')"

# 创建日志和PID目录
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_DIR="logs/parallel_training_${TIMESTAMP}"
PID_DIR="/tmp/training_pids_${TIMESTAMP}"
mkdir -p "$LOG_DIR"
mkdir -p "$PID_DIR"

echo ""
echo "📋 训练配置:"
echo "  日志目录: $LOG_DIR"
echo "  PID目录: $PID_DIR"
echo "  启动间隔: 5秒"
echo ""

# 保存会话信息
echo "LOG_DIR=$LOG_DIR" > /tmp/training_session_${TIMESTAMP}.env
echo "PID_DIR=$PID_DIR" >> /tmp/training_session_${TIMESTAMP}.env
echo "TIMESTAMP=$TIMESTAMP" >> /tmp/training_session_${TIMESTAMP}.env

echo "🚀 开始启动训练进程..."
echo ""

# 启动所有训练脚本（后台模式）
for stage in {01..12}; do
    echo "启动阶段${stage}训练..."
    
    # 后台启动训练进程
    nohup python scripts/parallel/train_stage_${stage}.py \
        > "$LOG_DIR/stage_${stage}.log" \
        2> "$LOG_DIR/stage_${stage}_error.log" &
    
    # 保存进程ID
    echo $! > "$PID_DIR/stage_${stage}.pid"
    
    echo "  ✅ 阶段${stage} PID: $! (日志: $LOG_DIR/stage_${stage}.log)"
    
    # 等待5秒避免并发冲突
    sleep 5
done

echo ""
echo "✅ 所有训练进程已启动"
echo ""
echo "📊 进程状态检查:"

# 检查所有进程状态
sleep 2
for stage in {01..12}; do
    if [ -f "$PID_DIR/stage_${stage}.pid" ]; then
        PID=$(cat "$PID_DIR/stage_${stage}.pid")
        if ps -p $PID > /dev/null 2>&1; then
            echo "  ✅ 阶段${stage}: 运行中 (PID: $PID)"
        else
            echo "  ❌ 阶段${stage}: 启动失败 (PID: $PID)"
        fi
    fi
done

echo ""
echo "💡 管理命令:"
echo "  查看所有进程: ps aux | grep train_stage"
echo "  查看日志: tail -f $LOG_DIR/stage_XX.log"
echo "  停止所有训练: bash scripts/parallel/stop_all_training.sh $TIMESTAMP"
echo "  监控训练: python scripts/parallel/monitor_training.py $TIMESTAMP"
echo ""
echo "📁 会话信息已保存到: /tmp/training_session_${TIMESTAMP}.env"
echo "🎯 预计总训练时间: 约21小时"
