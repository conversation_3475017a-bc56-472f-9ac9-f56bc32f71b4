{"timers": {"training_iteration": 2.348052268847823, "restore_env_runners": 7.82916322350502e-06, "training_step": 2.347881528083235, "env_runner_sampling_timer": 0.6293191439472139, "learner_update_timer": 1.7152431197464466, "synch_weights": 0.0028607118874788284}, "env_runners": {"sample": 0.12824721070218853, "num_agent_steps_sampled": {"agv_0": 1000.0}, "timers": {"connectors": {"add_time_dim_to_batch_and_zero_pad": 1.3581011444330215e-05, "add_states_from_episodes_to_batch": 5.353707820177078e-06, "numpy_to_tensor": 4.6144239604473114e-05, "agent_to_module_mapping": 6.136950105428696e-06, "add_observations_from_episodes_to_batch": 3.664987161755562e-05, "batch_individual_items": 3.166636452078819e-05}}, "num_agent_steps_sampled_lifetime": {"agv_0": 3000.0}, "env_to_module_connector": {"connector_pipeline_timer": 9.99502332814687e-05, "timers": {"connectors": {"add_states_from_episodes_to_batch": 2.44658775247773e-06, "numpy_to_tensor": 2.1048760294395325e-05, "add_time_dim_to_batch_and_zero_pad": 4.520408550141426e-06, "agent_to_module_mapping": 3.880186480620097e-06, "batch_individual_items": 1.3683246415085101e-05, "add_observations_from_episodes_to_batch": 1.5112468486858696e-05}}}, "episode_return_max": -99.3630555555556, "module_to_env_connector": {"timers": {"connectors": {"un_batch_to_individual_items": 1.329157279982385e-05, "normalize_and_clip_actions": 2.55762423471434e-05, "listify_data_for_vector_env": 5.92419858437136e-06, "module_to_agent_unmapping": 3.1246230091435594e-06, "get_actions": 0.00011076969159281253, "remove_single_ts_time_rank_from_batch": 1.1326983778183407e-06, "tensor_to_numpy": 4.0226881897784364e-05}}, "connector_pipeline_timer": 0.00025190315062109116}, "num_module_steps_sampled_lifetime": {"shared_policy": 3000.0}, "episode_len_min": 500, "agent_steps": {"agv_0": 500.0}, "weights_seq_no": 0.0, "episode_return_mean": -104.31590277777784, "num_module_steps_sampled": {"shared_policy": 1000.0}, "agent_episode_returns_mean": {"agv_0": -104.31590277777784}, "episode_len_max": 500, "env_to_module_sum_episodes_length_in": 112.6985638152698, "num_env_steps_sampled_lifetime": 3000.0, "time_between_sampling": 0.0005163425218777265, "env_reset_timer": 0.00011730613186955452, "num_episodes_lifetime": 4.0, "connector_pipeline_timer": 0.0003348272293806076, "episode_return_min": -119.17444444444456, "episode_duration_sec_mean": 0.309848964214325, "module_episode_returns_mean": {"shared_policy": -104.31590277777784}, "rlmodule_inference_timer": 8.917030755591516e-05, "env_step_timer": 5.726287698101466e-05, "num_episodes": 2.0, "episode_len_mean": 500.0, "env_to_module_sum_episodes_length_out": 112.6985638152698, "num_env_steps_sampled": 1000.0, "num_env_steps_sampled_lifetime_throughput": 1653.8063497050923}, "learners": {"shared_policy": {"curr_kl_coeff": 0.20000000298023224, "vf_loss_unclipped": "0.8936703", "num_module_steps_trained": 10080, "entropy": "1.5848897", "policy_loss": "0.09505233", "num_trainable_parameters": 176646, "vf_loss": "0.8936703", "total_loss": "0.46553496", "mean_kl_loss": "0.014459658", "curr_entropy_coeff": 0.05, "num_module_steps_trained_lifetime": 10080, "weights_seq_no": 1.0, "gradients_default_optimizer_global_norm": "1.8718575", "diff_num_grad_updates_vs_sampler_policy": "1.0", "module_train_batch_size_mean": 32.0, "vf_explained_var": "0.29512733", "default_optimizer_learning_rate": 0.0001, "num_module_steps_trained_lifetime_throughput": 9850.73767590523}, "__all_modules__": {"num_env_steps_trained": 315000, "learner_connector_sum_episodes_length_in": 1000, "learner_connector_sum_episodes_length_out": 1000, "learner_connector": {"timers": {"connectors": {"add_columns_from_episodes_to_train_batch": 0.00795908784493804, "general_advantage_estimation": 0.052013872656971216, "add_time_dim_to_batch_and_zero_pad": 1.5527941286563873e-05, "numpy_to_tensor": 0.0004332750104367733, "add_observations_from_episodes_to_batch": 6.218859925866127e-05, "add_one_ts_to_episodes_and_truncate": 0.0011961068958044052, "add_states_from_episodes_to_batch": 5.739275366067886e-06, "agent_to_module_mapping": 0.0005389591678977013, "batch_individual_items": 0.11002222215756774}}, "connector_pipeline_timer": 0.1725641069933772}, "num_module_steps_trained": 10080, "num_trainable_parameters": 176646, "num_env_steps_trained_lifetime": 315000, "num_non_trainable_parameters": 0, "num_module_steps_trained_lifetime": 10080, "num_env_steps_trained_lifetime_throughput": 307802.**********, "num_module_steps_trained_throughput": 9850.67755133727, "num_module_steps_trained_lifetime_throughput": 9850.57304573157}}, "num_training_step_calls_per_iteration": 1, "num_env_steps_sampled_lifetime": 3000.0, "fault_tolerance": {"num_healthy_workers": 0, "num_remote_worker_restarts": 0}, "env_runner_group": {"actor_manager_num_outstanding_async_reqs": 0}, "done": false, "training_iteration": 1, "trial_id": "default", "date": "2025-07-24_15-17-43", "timestamp": **********, "time_this_iter_s": 2.3494973182678223, "time_total_s": 2.3494973182678223, "pid": 3417189, "hostname": "521public", "node_ip": "*************", "config": {"exploration_config": {}, "extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 1, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "env": "multi_agent_agv_env", "env_config": {"curriculum_stage": 1, "env_config_path": "configs/environment_config.yaml", "render_mode": null}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": true, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 0, "create_local_env_runner": true, "num_envs_per_env_runner": 1, "gym_env_vectorize_mode": "SYNC", "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "episodes_to_numpy": true, "max_requests_in_flight_per_env_runner": 1, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "merge_env_runner_states": "training_only", "broadcast_env_runner_states": true, "episode_lookback_horizon": 1, "rollout_fragment_length": 200, "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "_is_online": true, "num_learners": 1, "num_gpus_per_learner": 1, "num_cpus_per_learner": "auto", "num_aggregator_actors_per_learner": 0, "max_requests_in_flight_per_aggregator_actor": 3, "local_gpu_idx": 0, "max_requests_in_flight_per_learner": 3, "gamma": 0.99, "lr": 0.0001, "grad_clip": 1.0, "grad_clip_by": "global_norm", "_train_batch_size_per_learner": null, "train_batch_size": 1000, "num_epochs": 10, "minibatch_size": 32, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {}, "_learner_class": null, "callbacks_on_algorithm_init": null, "callbacks_on_env_runners_recreated": null, "callbacks_on_offline_eval_runners_recreated": null, "callbacks_on_checkpoint_loaded": null, "callbacks_on_environment_created": null, "callbacks_on_episode_created": null, "callbacks_on_episode_start": null, "callbacks_on_episode_step": null, "callbacks_on_episode_end": null, "callbacks_on_evaluate_start": null, "callbacks_on_evaluate_end": null, "callbacks_on_evaluate_offline_start": null, "callbacks_on_evaluate_offline_end": null, "callbacks_on_sample_end": null, "callbacks_on_train_result": null, "explore": true, "enable_rl_module_and_learner": true, "enable_env_runner_and_connector_v2": true, "_prior_exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function MAPPOConfig.create_multi_agent_config.<locals>.policy_mapping_fn at 0x7f7309ac96c0>", "policies_to_train": ["shared_policy"], "policy_states_are_swappable": false, "observation_fn": null, "offline_data_class": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "ignore_final_observation": false, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_remaining_data": false, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": 20, "evaluation_duration": 5, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_auto_duration_min_env_steps_per_sample": 100, "evaluation_auto_duration_max_env_steps_per_sample": 2000, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "offline_evaluation_interval": null, "num_offline_eval_runners": 0, "offline_loss_for_module_fn": null, "offline_evaluation_duration": 1, "offline_evaluation_parallel_to_training": false, "offline_evaluation_timeout_s": 120.0, "num_cpus_per_offline_eval_runner": 1, "num_gpus_per_offline_eval_runner": 0, "custom_resources_per_offline_eval_runner": {}, "restart_failed_offline_eval_runners": true, "ignore_offline_eval_runner_failures": false, "max_num_offline_eval_runner_restarts": 1000, "offline_eval_runner_restore_timeout_s": 1800.0, "max_requests_in_flight_per_offline_eval_runner": 1, "validate_offline_eval_runners_after_construction": true, "offline_eval_runner_health_probe_timeout_s": 30.0, "offline_eval_rl_module_inference_only": false, "broadcast_offline_eval_runner_states": false, "offline_eval_batch_size_per_runner": 256, "dataset_num_iters_per_eval_runner": 1, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_validate_config": true, "_use_msgpack_checkpoints": false, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "env_task_fn": -1, "enable_connectors": -1, "simple_optimizer": true, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 0.5, "entropy_coeff": 0.05, "clip_param": 0.2, "vf_clip_param": 10.0, "entropy_coeff_schedule": null, "lr_schedule": null, "sgd_minibatch_size": -1, "vf_share_layers": -1, "class": "<class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>", "lambda": 0.95, "input": "sampler", "policies": {"shared_policy": [null, "Box(-inf, inf, (84,), float32)", "Discrete(5)", {"model": {"custom_model": "attention_policy", "custom_model_config": {"max_agvs": 4, "max_tasks": 16, "agv_feature_dim": 5, "task_feature_dim": 4, "attention_config_path": "configs/attention_config.yaml"}}}]}, "callbacks": "<class 'ray.rllib.callbacks.callbacks.RLlibCallback'>", "create_env_on_driver": false, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 2.3494973182678223, "iterations_since_restore": 1, "perf": {"cpu_util_percent": 5.2749999999999995, "ram_util_percent": 21.7}}