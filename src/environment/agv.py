"""
AGV智能体类
定义AGV的状态、行为和属性
"""

import numpy as np
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass
from enum import IntEnum

class Action(IntEnum):
    """AGV动作枚举"""
    UP = 0
    DOWN = 1
    LEFT = 2
    RIGHT = 3
    WAIT = 4

@dataclass
class Task:
    """任务数据类"""
    id: int
    x: int
    y: int
    weight: int
    state: int = 0  # 0: 未分配, 1: 已分配, 2: 已完成
    
    def to_array(self) -> np.ndarray:
        """转换为数组表示"""
        return np.array([self.x, self.y, self.weight, self.state], dtype=np.float32)

class AGV:
    """AGV智能体类"""
    
    def __init__(self, agv_id: int, x: int, y: int, max_capacity: int = 25):
        """
        初始化AGV
        
        Args:
            agv_id: AGV唯一标识
            x, y: 初始位置
            max_capacity: 最大载重能力
        """
        self.id = agv_id
        self.x = x
        self.y = y
        self.max_capacity = max_capacity
        
        # 状态变量
        self.current_load = 0           # 当前载重
        self.task_queue = []            # 任务队列
        self.assigned_tasks = []        # 已分配任务列表
        self.completed_tasks = []       # 已完成任务列表
        
        # 统计信息
        self.total_distance = 0         # 总移动距离
        self.total_tasks_completed = 0  # 完成任务总数
        self.collision_count = 0        # 碰撞次数
        
    def get_position(self) -> Tuple[int, int]:
        """获取当前位置"""
        return (self.x, self.y)
    
    def set_position(self, x: int, y: int) -> None:
        """设置位置"""
        self.x = x
        self.y = y
    
    def move(self, action: Action) -> Tuple[int, int]:
        """
        根据动作移动AGV
        
        Args:
            action: 移动动作
            
        Returns:
            Tuple[int, int]: 新位置 (x, y)
        """
        new_x, new_y = self.x, self.y
        
        if action == Action.UP:
            new_y -= 1
        elif action == Action.DOWN:
            new_y += 1
        elif action == Action.LEFT:
            new_x -= 1
        elif action == Action.RIGHT:
            new_x += 1
        elif action == Action.WAIT:
            pass  # 保持原位置
        
        return new_x, new_y
    
    def execute_move(self, action: Action, new_x: int, new_y: int) -> None:
        """
        执行移动并更新统计信息
        
        Args:
            action: 执行的动作
            new_x, new_y: 新位置
        """
        if action != Action.WAIT:
            # 计算移动距离
            distance = abs(new_x - self.x) + abs(new_y - self.y)
            self.total_distance += distance
        
        # 更新位置
        self.x = new_x
        self.y = new_y
    
    def can_take_task(self, task: Task) -> bool:
        """
        检查是否可以接受任务
        
        Args:
            task: 待检查的任务
            
        Returns:
            bool: 是否可以接受
        """
        return self.current_load + task.weight <= self.max_capacity
    
    def assign_task(self, task: Task) -> bool:
        """
        分配任务给AGV
        
        Args:
            task: 要分配的任务
            
        Returns:
            bool: 是否分配成功
        """
        if self.can_take_task(task):
            self.assigned_tasks.append(task)
            self.current_load += task.weight
            task.state = 1  # 标记为已分配
            return True
        return False
    
    def complete_task(self, task: Task) -> bool:
        """
        完成任务
        
        Args:
            task: 要完成的任务
            
        Returns:
            bool: 是否完成成功
        """
        if task in self.assigned_tasks:
            self.assigned_tasks.remove(task)
            self.completed_tasks.append(task)
            self.current_load -= task.weight
            self.total_tasks_completed += 1
            task.state = 2  # 标记为已完成
            return True
        return False
    
    def get_load_utilization(self) -> float:
        """
        获取载重利用率
        
        Returns:
            float: 载重利用率 (0-1)
        """
        return self.current_load / self.max_capacity
    
    def get_queue_length(self) -> int:
        """获取任务队列长度"""
        return len(self.assigned_tasks)
    
    def to_array(self) -> np.ndarray:
        """
        转换为数组表示（用于神经网络输入）
        
        Returns:
            np.ndarray: AGV状态数组 [x, y, current_load, max_capacity, queue_length]
        """
        return np.array([
            self.x,
            self.y,
            self.current_load,
            self.max_capacity,
            self.get_queue_length()
        ], dtype=np.float32)
    
    def get_state_dict(self) -> Dict:
        """
        获取AGV状态字典
        
        Returns:
            Dict: 状态信息
        """
        return {
            'id': self.id,
            'position': (self.x, self.y),
            'current_load': self.current_load,
            'max_capacity': self.max_capacity,
            'load_utilization': self.get_load_utilization(),
            'queue_length': self.get_queue_length(),
            'assigned_tasks': len(self.assigned_tasks),
            'completed_tasks': len(self.completed_tasks),
            'total_distance': self.total_distance,
            'collision_count': self.collision_count
        }
    
    def reset(self, x: int, y: int) -> None:
        """
        重置AGV状态
        
        Args:
            x, y: 重置位置
        """
        self.x = x
        self.y = y
        self.current_load = 0
        self.task_queue.clear()
        self.assigned_tasks.clear()
        self.completed_tasks.clear()
        self.total_distance = 0
        self.total_tasks_completed = 0
        self.collision_count = 0

if __name__ == "__main__":
    # 测试AGV类
    print("🤖 测试AGV类...")
    
    # 创建AGV
    agv = AGV(agv_id=0, x=0, y=0, max_capacity=25)
    print(f"AGV初始状态: {agv.get_state_dict()}")
    
    # 创建任务
    task1 = Task(id=0, x=5, y=5, weight=10)
    task2 = Task(id=1, x=10, y=8, weight=20)
    
    # 测试任务分配
    print(f"分配任务1: {agv.assign_task(task1)}")
    print(f"分配任务2: {agv.assign_task(task2)}")  # 应该失败，超重
    
    print(f"AGV状态: {agv.get_state_dict()}")
    print(f"AGV数组表示: {agv.to_array()}")
    
    # 测试移动
    new_pos = agv.move(Action.RIGHT)
    agv.execute_move(Action.RIGHT, *new_pos)
    print(f"移动后位置: {agv.get_position()}")
    
    print("✅ AGV类测试完成！")
