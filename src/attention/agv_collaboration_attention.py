"""
第二层注意力机制：AGV协作注意力
实现AGV之间的协作注意力计算，考虑相对位置、载重差异和协作半径
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import yaml
from typing import Dict, Tuple, Optional
import math

from .agv_task_attention import MultiHeadAttention

class AGVCollaborationAttention(nn.Module):
    """AGV协作注意力层"""
    
    def __init__(self, config_path: str = "configs/attention_config.yaml"):
        """
        初始化AGV协作注意力
        
        Args:
            config_path: 配置文件路径
        """
        super().__init__()
        
        # 加载配置
        self.config = self._load_config(config_path)
        self.layer2_config = self.config['layer2_agv_collaboration']
        self.attention_config = self.config['attention']
        
        # 网络参数
        self.hidden_dim = self.attention_config['dimensions']['hidden_dim']
        self.attention_dim = self.attention_config['dimensions']['attention_dim']
        self.num_heads = self.layer2_config['architecture']['attention_heads']
        self.dropout_rate = self.layer2_config['architecture']['dropout_rate']
        
        # AGV特征编码器（包含协作特征）
        agv_layers = self.layer2_config['architecture']['agv_encoder_layers']
        # 输入维度：5(基础特征) + 2(相对位置) + 1(载重差异) + 1(载重能力相似度) + 1(工作负载均衡度) = 10
        self.agv_encoder = self._build_mlp([10] + agv_layers)
        
        # 多头注意力
        self.attention = MultiHeadAttention(
            d_model=self.hidden_dim,
            num_heads=self.num_heads,
            dropout=self.dropout_rate
        )
        
        # 输出投影
        self.output_projection = nn.Linear(self.hidden_dim, self.attention_config['dimensions']['output_dim'])
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _build_mlp(self, layer_sizes: list) -> nn.Module:
        """构建MLP网络"""
        layers = []
        for i in range(len(layer_sizes) - 1):
            layers.append(nn.Linear(layer_sizes[i], layer_sizes[i + 1]))
            if i < len(layer_sizes) - 2:  # 最后一层不加激活函数
                layers.append(nn.ReLU())
                layers.append(nn.Dropout(self.dropout_rate))
        return nn.Sequential(*layers)
    
    def forward(self, agv_features: torch.Tensor, agv_positions: torch.Tensor,
                agv_loads: torch.Tensor, agv_capacities: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_features: AGV基础特征 [batch_size, num_agvs, 5]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_loads: AGV当前载重 [batch_size, num_agvs]
            agv_capacities: AGV载重能力 [batch_size, num_agvs]
            
        Returns:
            attention_output: 注意力输出 [batch_size, num_agvs, output_dim]
            attention_weights: 注意力权重 [batch_size, num_heads, num_agvs, num_agvs]
        """
        batch_size, num_agvs, _ = agv_features.size()
        
        # 计算协作特征
        collaboration_features = self._compute_collaboration_features(
            agv_features, agv_positions, agv_loads, agv_capacities
        )
        
        # 编码AGV特征（包含协作特征）
        agv_encoded = self.agv_encoder(collaboration_features)  # [batch_size, num_agvs, hidden_dim]
        
        # 生成掩码
        mask = self._generate_collaboration_mask(agv_positions, agv_loads)
        
        # 计算自注意力（AGV之间的注意力）
        attention_output, attention_weights = self.attention(
            query=agv_encoded,
            key=agv_encoded,
            value=agv_encoded,
            mask=mask
        )
        
        # 输出投影
        output = self.output_projection(attention_output)
        
        return output, attention_weights
    
    def _compute_collaboration_features(self, agv_features: torch.Tensor, agv_positions: torch.Tensor,
                                      agv_loads: torch.Tensor, agv_capacities: torch.Tensor) -> torch.Tensor:
        """
        计算AGV协作特征

        Args:
            agv_features: AGV基础特征 [batch_size, num_agvs, 5]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_loads: AGV当前载重 [batch_size, num_agvs]
            agv_capacities: AGV载重能力 [batch_size, num_agvs]

        Returns:
            collaboration_features: 协作特征 [batch_size, num_agvs, 10]
        """
        batch_size, num_agvs, _ = agv_features.size()
        
        # 计算AGV之间的平均相对位置
        agv_pos_expanded = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        other_pos_expanded = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
        
        # 相对位置差异
        relative_positions = agv_pos_expanded - other_pos_expanded  # [batch_size, num_agvs, num_agvs, 2]
        
        # 计算每个AGV与其他AGV的平均相对位置
        # 排除自己（对角线元素）
        mask = torch.eye(num_agvs, device=agv_positions.device).unsqueeze(0).expand(batch_size, -1, -1)
        mask = mask.unsqueeze(-1)  # [batch_size, num_agvs, num_agvs, 1]
        
        masked_relative_pos = relative_positions * (1 - mask)
        avg_relative_pos = masked_relative_pos.sum(dim=2) / (num_agvs - 1)  # [batch_size, num_agvs, 2]
        
        # 计算载重差异
        agv_loads_expanded = agv_loads.unsqueeze(2)  # [batch_size, num_agvs, 1]
        other_loads_expanded = agv_loads.unsqueeze(1)  # [batch_size, 1, num_agvs]
        
        load_differences = agv_loads_expanded - other_loads_expanded  # [batch_size, num_agvs, num_agvs]
        
        # 排除自己，计算平均载重差异
        mask_1d = torch.eye(num_agvs, device=agv_positions.device).unsqueeze(0).expand(batch_size, -1, -1)
        masked_load_diff = load_differences * (1 - mask_1d)
        avg_load_diff = masked_load_diff.sum(dim=2, keepdim=True) / (num_agvs - 1)  # [batch_size, num_agvs, 1]

        # 计算载重能力相似度
        capacity_diff = torch.abs(agv_capacities.unsqueeze(2) - agv_capacities.unsqueeze(1))  # [batch_size, num_agvs, num_agvs]
        masked_capacity_diff = capacity_diff * (1 - mask_1d)
        avg_capacity_similarity = 1.0 / (1.0 + masked_capacity_diff.sum(dim=2, keepdim=True) / (num_agvs - 1))  # [batch_size, num_agvs, 1]

        # 计算工作负载均衡度
        load_ratios = agv_loads / (agv_capacities + 1e-8)  # [batch_size, num_agvs]
        load_ratio_diff = torch.abs(load_ratios.unsqueeze(2) - load_ratios.unsqueeze(1))  # [batch_size, num_agvs, num_agvs]
        masked_ratio_diff = load_ratio_diff * (1 - mask_1d)
        avg_load_balance = 1.0 / (1.0 + masked_ratio_diff.sum(dim=2, keepdim=True) / (num_agvs - 1))  # [batch_size, num_agvs, 1]

        # 拼接所有特征
        collaboration_features = torch.cat([
            agv_features,           # [batch_size, num_agvs, 5] - 基础特征
            avg_relative_pos,       # [batch_size, num_agvs, 2] - 平均相对位置
            avg_load_diff,          # [batch_size, num_agvs, 1] - 平均载重差异
            avg_capacity_similarity, # [batch_size, num_agvs, 1] - 载重能力相似度
            avg_load_balance        # [batch_size, num_agvs, 1] - 工作负载均衡度
        ], dim=-1)  # [batch_size, num_agvs, 10]
        
        return collaboration_features
    
    def _generate_collaboration_mask(self, agv_positions: torch.Tensor,
                                   agv_loads: torch.Tensor) -> torch.Tensor:
        """
        生成协作注意力掩码
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_loads: AGV载重 [batch_size, num_agvs]
            
        Returns:
            mask: 掩码张量 [batch_size, num_agvs, num_agvs]
        """
        batch_size, num_agvs, _ = agv_positions.size()
        
        # 初始化掩码（全为1，表示可以注意）
        mask = torch.ones(batch_size, num_agvs, num_agvs, device=agv_positions.device)
        
        masking_config = self.layer2_config['masking']
        
        # 1. 自注意力掩码：AGV不关注自己
        if masking_config['enable_self_mask']:
            self_mask = torch.eye(num_agvs, device=agv_positions.device)
            self_mask = self_mask.unsqueeze(0).expand(batch_size, -1, -1)
            mask = mask * (1 - self_mask)
        
        # 2. 距离掩码：只关注协作半径内的其他AGV
        if masking_config['enable_distance_mask']:
            collaboration_radius = masking_config['collaboration_radius']

            # 计算AGV之间的曼哈顿距离
            agv_pos_expanded = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
            other_pos_expanded = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]

            distances = torch.sum(torch.abs(agv_pos_expanded - other_pos_expanded), dim=-1)
            distance_mask = (distances <= collaboration_radius).float()
            mask = mask * distance_mask

        # 3. 载重差异掩码：载重相似的AGV更容易协作
        if masking_config['enable_load_mask']:
            load_diff_threshold = 15.0  # 载重差异阈值

            agv_loads_expanded = agv_loads.unsqueeze(2)  # [batch_size, num_agvs, 1]
            other_loads_expanded = agv_loads.unsqueeze(1)  # [batch_size, 1, num_agvs]

            load_differences = torch.abs(agv_loads_expanded - other_loads_expanded)
            load_mask = (load_differences <= load_diff_threshold).float()
            mask = mask * load_mask

        # 4. 处理全零掩码的情况（协作fallback机制）
        mask_sums = mask.sum(dim=-1, keepdim=True)  # [batch_size, num_agvs, 1]
        zero_mask_agvs = (mask_sums == 0).squeeze(-1)  # [batch_size, num_agvs]

        if zero_mask_agvs.any():
            # 为完全孤立的AGV提供最小协作连接
            agv_pos_expanded = agv_positions.unsqueeze(2)
            other_pos_expanded = agv_positions.unsqueeze(1)
            distances = torch.sum(torch.abs(agv_pos_expanded - other_pos_expanded), dim=-1)

            for batch_idx in range(batch_size):
                for agv_idx in range(num_agvs):
                    if zero_mask_agvs[batch_idx, agv_idx]:
                        # 找到最近的AGV进行最小协作
                        agv_distances = distances[batch_idx, agv_idx]
                        agv_distances[agv_idx] = float('inf')  # 排除自己

                        if agv_distances.min() < float('inf'):
                            nearest_agv = torch.argmin(agv_distances)
                            mask[batch_idx, agv_idx, nearest_agv] = 0.1  # 给予最小协作权重

        return mask
    
    def get_collaboration_metrics(self, attention_weights: torch.Tensor,
                                agv_positions: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算协作指标
        
        Args:
            attention_weights: 注意力权重 [batch_size, num_heads, num_agvs, num_agvs]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            
        Returns:
            metrics: 协作指标字典
        """
        # 平均注意力权重（跨头）
        avg_attention = attention_weights.mean(dim=1)  # [batch_size, num_agvs, num_agvs]
        
        # 协作强度：每个AGV对其他AGV的平均注意力
        collaboration_intensity = avg_attention.sum(dim=-1)  # [batch_size, num_agvs]
        
        # 注意力熵：衡量注意力分布的均匀性
        attention_entropy = -torch.sum(avg_attention * torch.log(avg_attention + 1e-8), dim=-1)
        
        # 协作距离：注意力加权的平均距离
        agv_pos_expanded = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        other_pos_expanded = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
        distances = torch.norm(agv_pos_expanded - other_pos_expanded, dim=-1)  # [batch_size, num_agvs, num_agvs]
        
        weighted_distances = (avg_attention * distances).sum(dim=-1)  # [batch_size, num_agvs]
        
        return {
            'collaboration_intensity': collaboration_intensity,
            'attention_entropy': attention_entropy,
            'weighted_collaboration_distance': weighted_distances,
            'avg_attention_weights': avg_attention
        }
