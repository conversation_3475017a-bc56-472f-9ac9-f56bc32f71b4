#!/usr/bin/env python3
"""
主实验执行脚本：双层注意力MAPPO训练
执行16000 episodes的完整课程学习训练
在biye_RL conda环境中运行，使用GPU加速
"""

import os
import sys
import time
import json
import torch
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.mappo.curriculum_trainer import CurriculumTrainer


def main():
    """执行主实验"""
    print("🚀 开始执行主实验：双层注意力MAPPO训练")
    print("=" * 60)

    # 验证GPU环境
    print("🔧 GPU环境检查:")
    print(f"  PyTorch版本: {torch.__version__}")
    print(f"  CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"  GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"  GPU数量: {torch.cuda.device_count()}")
    print()

    # 实验配置
    experiment_name = f"dual_attention_mappo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    config_path = "configs/mappo_config.yaml"

    print(f"📋 实验配置:")
    print(f"  实验名称: {experiment_name}")
    print(f"  配置文件: {config_path}")
    print(f"  目标episodes: 16000")
    print(f"  课程学习阶段: 12个")
    print(f"  可视化监控: 启用")
    print(f"  GPU加速: {'启用' if torch.cuda.is_available() else '禁用'}")
    print()
    
    # 记录实验开始时间
    experiment_start_time = time.time()
    
    try:
        # 创建课程学习训练器
        print("🎓 初始化课程学习训练器...")
        trainer = CurriculumTrainer(
            config_path=config_path,
            experiment_name=experiment_name,
            enable_visualization=True
        )
        print("✅ 训练器初始化完成")
        print()
        
        # 执行完整的课程学习训练
        print("🎯 开始执行完整课程学习训练...")
        training_result = trainer.train_curriculum(
            start_stage=1,
            end_stage=12
        )
        
        # 记录实验结束时间
        experiment_end_time = time.time()
        total_experiment_time = experiment_end_time - experiment_start_time
        
        # 输出实验结果摘要
        print("\n" + "=" * 60)
        print("🎉 主实验执行完成！")
        print("=" * 60)
        print(f"📊 实验结果摘要:")
        print(f"  实验名称: {training_result['experiment_name']}")
        print(f"  总训练时长: {total_experiment_time:.1f}秒 ({total_experiment_time/3600:.2f}小时)")
        print(f"  总episodes: {training_result['total_episodes']}")
        print(f"  完成阶段: {training_result['stages_completed']}")
        print(f"  最终阶段: {training_result['final_stage']}")
        
        # 保存实验摘要
        experiment_summary = {
            "experiment_type": "main_experiment",
            "algorithm": "dual_attention_mappo",
            "experiment_name": experiment_name,
            "start_time": experiment_start_time,
            "end_time": experiment_end_time,
            "total_duration": total_experiment_time,
            "training_result": training_result,
            "config_used": config_path,
            "success": True
        }
        
        summary_path = os.path.join("results", experiment_name, "experiment_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(experiment_summary, f, indent=2, ensure_ascii=False)
        
        print(f"  实验摘要保存至: {summary_path}")
        print()
        
        # 输出下一步建议
        print("📋 下一步建议:")
        print("  1. 检查训练结果和可视化图表")
        print("  2. 执行500 episodes性能测试")
        print("  3. 运行基线对比实验")
        print("  4. 进行消融实验验证")
        
        return True
        
    except Exception as e:
        print(f"❌ 主实验执行失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 保存错误信息
        error_summary = {
            "experiment_type": "main_experiment",
            "algorithm": "dual_attention_mappo",
            "experiment_name": experiment_name,
            "start_time": experiment_start_time,
            "end_time": time.time(),
            "error": str(e),
            "traceback": traceback.format_exc(),
            "success": False
        }
        
        error_path = os.path.join("results", experiment_name, "experiment_error.json")
        os.makedirs(os.path.dirname(error_path), exist_ok=True)
        with open(error_path, 'w', encoding='utf-8') as f:
            json.dump(error_summary, f, indent=2, ensure_ascii=False)
        
        return False
        
    finally:
        # 清理资源
        if 'trainer' in locals():
            print("🧹 清理训练器资源...")
            trainer.cleanup()
            print("✅ 资源清理完成")


if __name__ == "__main__":
    success = main()
    exit_code = 0 if success else 1
    print(f"\n🏁 主实验执行{'成功' if success else '失败'}，退出码: {exit_code}")
    sys.exit(exit_code)
