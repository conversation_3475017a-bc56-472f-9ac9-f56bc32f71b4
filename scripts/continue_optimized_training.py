#!/usr/bin/env python3
"""
继续优化训练脚本：应用改进的超参数继续当前训练
"""

import os
import sys
import time
import json
import torch
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.mappo.curriculum_trainer import CurriculumTrainer


def continue_optimized_training():
    """继续优化后的训练"""
    print("🚀 开始继续优化训练")
    print("=" * 60)
    
    # 验证GPU环境
    print("🔧 GPU环境检查:")
    print(f"  PyTorch版本: {torch.__version__}")
    print(f"  CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"  GPU设备: {torch.cuda.get_device_name(0)}")
    print()
    
    # 应用的优化
    print("⚡ 应用的训练优化:")
    print("  1. 学习率: 0.0003 → 0.0001 (提高稳定性)")
    print("  2. 熵系数: 0.01 → 0.05 (增加探索)")
    print("  3. 梯度裁剪: 0.5 → 1.0 (放宽限制)")
    print("  4. 批大小: 2000 → 1000 (提高稳定性)")
    print("  5. SGD批大小: 64 → 32 (提高稳定性)")
    print("  6. SGD迭代: 8 → 10 (增加学习)")
    print("  7. 任务完成奖励: 5.0 → 10.0 (增加正奖励)")
    print("  8. 移动惩罚: -0.1 → -0.05 (减少惩罚)")
    print("  9. 新增进度奖励: 0.1 (中间奖励)")
    print("  10. 新增协作奖励: 2.0 (多智能体协作)")
    print()
    
    try:
        # 创建训练器
        experiment_name = f"optimized_dual_attention_mappo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        config_path = "configs/mappo_config.yaml"
        
        print("🎓 初始化优化的课程学习训练器...")
        trainer = CurriculumTrainer(
            config_path=config_path,
            experiment_name=experiment_name,
            enable_visualization=True
        )
        
        print("✅ 训练器初始化完成")
        print(f"  实验名称: {experiment_name}")
        print(f"  配置文件: {config_path}")
        print()
        
        # 从阶段5开始训练（应用优化）
        print("🎯 开始优化训练...")
        print("  起始阶段: 5 (2个AGV, 6个任务)")
        print("  目标阶段: 12")
        print("  应用优化: 已启用")
        print()
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行优化训练
        training_result = trainer.train_curriculum(
            start_stage=5,
            end_stage=12
        )
        
        # 记录完成时间
        end_time = time.time()
        total_time = end_time - start_time
        
        # 输出训练结果
        print("\n" + "=" * 60)
        print("🎉 优化训练执行完成！")
        print("=" * 60)
        print(f"📊 训练结果摘要:")
        print(f"  实验名称: {training_result['experiment_name']}")
        print(f"  训练时长: {total_time:.1f}秒 ({total_time/3600:.2f}小时)")
        print(f"  总episodes: {training_result['total_episodes']}")
        print(f"  完成阶段: {training_result['stages_completed']}")
        print(f"  最终阶段: {training_result['final_stage']}")
        
        # 保存实验摘要
        results_dir = trainer.results_dir
        summary_path = os.path.join(results_dir, "experiment_summary.json")
        experiment_summary = {
            "experiment_type": "optimized_training",
            "algorithm": "dual_attention_mappo",
            "experiment_name": experiment_name,
            "start_time": start_time,
            "end_time": end_time,
            "duration": total_time,
            "training_result": training_result,
            "config_used": config_path,
            "optimizations_applied": {
                "learning_rate_reduced": True,
                "entropy_increased": True,
                "gradient_clip_relaxed": True,
                "batch_size_reduced": True,
                "rewards_rebalanced": True,
                "intermediate_rewards_added": True
            },
            "success": True
        }
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(experiment_summary, f, indent=2, ensure_ascii=False)
        
        print(f"  实验摘要保存至: {summary_path}")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ 优化训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        if 'trainer' in locals():
            print("🧹 清理训练器资源...")
            trainer.cleanup()
            print("✅ 资源清理完成")


def main():
    """主函数"""
    print("🔄 继续优化训练")
    print("应用改进的超参数和奖励函数")
    print("从阶段5开始，使用优化配置")
    print()
    
    # 确认继续
    response = input("确认开始优化训练? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ 训练已取消")
        return
    
    # 执行优化训练
    success = continue_optimized_training()
    exit_code = 0 if success else 1
    print(f"\n🏁 优化训练{'成功' if success else '失败'}，退出码: {exit_code}")
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
