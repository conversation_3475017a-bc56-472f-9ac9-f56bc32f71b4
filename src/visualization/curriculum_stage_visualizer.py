#!/usr/bin/env python3
"""
课程学习阶段可视化验证器
对12个课程学习阶段的AGV运行场景进行可视化验证
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
import yaml
from typing import Dict, List, Tuple, Any
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.environment.agv_warehouse_env import AGVWarehouseEnv
from src.mappo.mappo_config import MAPPOConfig


class CurriculumStageVisualizer:
    """
    课程学习阶段可视化器

    功能：
    - 可视化每个课程学习阶段的AGV和任务配置
    - 生成静态场景图和动态运行演示
    - 验证阶段配置的正确性
    - 保存可视化结果
    """

    def __init__(self, config_path: str = "configs/mappo_config.yaml"):
        """
        初始化可视化器

        Args:
            config_path: MAPPO配置文件路径
        """
        self.mappo_config = MAPPOConfig(config_path)
        self.curriculum_stages = self.mappo_config.get_curriculum_stages()

        # 可视化配置
        self.fig_size = (12, 8)
        self.colors = {
            'agv': ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
            'task': '#FFA07A',
            'shelf': '#D3D3D3',
            'wall': '#2C3E50',
            'floor': '#ECF0F1',
            'path': '#3498DB'
        }

        # 结果保存目录
        self.results_dir = "results/curriculum_visualization"
        os.makedirs(self.results_dir, exist_ok=True)

        print(f"🎨 课程学习阶段可视化器初始化完成")
        print(f"  课程阶段数: {len(self.curriculum_stages)}")
        print(f"  结果保存目录: {self.results_dir}")

    def visualize_stage_static(self, stage: int, save_fig: bool = True) -> plt.Figure:
        """
        可视化指定阶段的静态场景

        Args:
            stage: 课程学习阶段
            save_fig: 是否保存图片

        Returns:
            plt.Figure: 生成的图形对象
        """
        if stage not in self.curriculum_stages:
            raise ValueError(f"阶段 {stage} 不存在")

        stage_config = self.curriculum_stages[stage]
        agv_num = stage_config['agv_num']
        task_num = stage_config['task_num']

        print(f"🎯 可视化阶段 {stage}: {agv_num}个AGV, {task_num}个任务")

        # 创建环境
        env = AGVWarehouseEnv(
            config_path="configs/environment_config.yaml",
            curriculum_stage=stage,
            render_mode=None
        )

        # 重置环境获取初始状态
        obs, info = env.reset(seed=42)

        # 创建图形
        fig, ax = plt.subplots(figsize=self.fig_size)

        # 绘制地图
        self._draw_map(ax, env)

        # 绘制AGV
        self._draw_agvs(ax, env)

        # 绘制任务
        self._draw_tasks(ax, env)

        # 设置标题和标签
        ax.set_title(f'课程学习阶段 {stage} - {agv_num}个AGV, {task_num}个任务',
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X坐标', fontsize=12)
        ax.set_ylabel('Y坐标', fontsize=12)

        # 添加图例
        self._add_legend(ax)

        # 添加阶段信息
        self._add_stage_info(ax, stage_config)

        # 设置坐标轴
        ax.set_xlim(-1, env.map_generator.width)
        ax.set_ylim(-1, env.map_generator.height)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)

        # 保存图片
        if save_fig:
            filename = f"stage_{stage}_static.png"
            filepath = os.path.join(self.results_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            print(f"  💾 静态场景图已保存: {filepath}")

        env.close()
        return fig

    def _draw_map(self, ax, env):
        """绘制地图"""
        # 获取地图矩阵
        map_grid = env.map_grid

        # 绘制地板和货架
        for y in range(env.map_generator.height):
            for x in range(env.map_generator.width):
                if map_grid[y, x] == env.map_generator.EMPTY:
                    # 绘制地板
                    rect = patches.Rectangle((x, y), 1, 1,
                                           linewidth=0.5, edgecolor='gray',
                                           facecolor=self.colors['floor'], alpha=0.3)
                    ax.add_patch(rect)
                elif map_grid[y, x] == env.map_generator.SHELF:
                    # 绘制货架
                    rect = patches.Rectangle((x, y), 1, 1,
                                           linewidth=1, edgecolor='black',
                                           facecolor=self.colors['shelf'])
                    ax.add_patch(rect)
                elif map_grid[y, x] in [env.map_generator.START, env.map_generator.END]:
                    # 绘制起点/终点
                    rect = patches.Rectangle((x, y), 1, 1,
                                           linewidth=0.5, edgecolor='gray',
                                           facecolor=self.colors['floor'], alpha=0.3)
                    ax.add_patch(rect)

    def _draw_agvs(self, ax, env):
        """绘制AGV"""
        for i, agv in enumerate(env.agvs):
            if i < len(self.colors['agv']):
                color = self.colors['agv'][i]
            else:
                color = self.colors['agv'][i % len(self.colors['agv'])]

            # 绘制AGV圆形
            circle = patches.Circle((agv.x + 0.5, agv.y + 0.5), 0.3,
                                  facecolor=color, edgecolor='black', linewidth=2)
            ax.add_patch(circle)

            # 添加AGV编号
            ax.text(agv.x + 0.5, agv.y + 0.5, f'A{i+1}',
                   ha='center', va='center', fontweight='bold', fontsize=10)

            # 显示载重信息
            load_text = f'{agv.current_load}/{agv.max_capacity}'
            ax.text(agv.x + 0.5, agv.y - 0.2, load_text,
                   ha='center', va='center', fontsize=8,
                   bbox=dict(boxstyle="round,pad=0.1", facecolor='white', alpha=0.8))

    def _draw_tasks(self, ax, env):
        """绘制任务"""
        for i, task in enumerate(env.tasks):
            if task.state == 0:  # 未分配任务
                # 绘制任务方块
                rect = patches.Rectangle((task.x, task.y), 1, 1,
                                       linewidth=2, edgecolor='red',
                                       facecolor=self.colors['task'], alpha=0.7)
                ax.add_patch(rect)

                # 添加任务编号和重量
                ax.text(task.x + 0.5, task.y + 0.5, f'T{i+1}',
                       ha='center', va='center', fontweight='bold', fontsize=9)
                ax.text(task.x + 0.5, task.y + 0.2, f'W:{task.weight}',
                       ha='center', va='center', fontsize=7)

    def _add_legend(self, ax):
        """添加图例"""
        legend_elements = [
            patches.Patch(color=self.colors['agv'][0], label='AGV'),
            patches.Patch(color=self.colors['task'], label='任务'),
            patches.Patch(color=self.colors['shelf'], label='货架'),
            patches.Patch(color=self.colors['floor'], label='地板', alpha=0.3)
        ]
        ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))

    def _add_stage_info(self, ax, stage_config):
        """添加阶段信息"""
        info_text = f"""阶段信息:
AGV数量: {stage_config['agv_num']}
任务数量: {stage_config['task_num']}
最大步数: {stage_config['max_steps']}
目标episodes: {stage_config['episodes']}"""

        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=10,
               bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))

    def visualize_stage_dynamic(self, stage: int, steps: int = 50, save_gif: bool = True) -> str:
        """
        可视化指定阶段的动态运行

        Args:
            stage: 课程学习阶段
            steps: 运行步数
            save_gif: 是否保存GIF动画

        Returns:
            str: 保存的GIF文件路径
        """
        if stage not in self.curriculum_stages:
            raise ValueError(f"阶段 {stage} 不存在")

        stage_config = self.curriculum_stages[stage]
        agv_num = stage_config['agv_num']
        task_num = stage_config['task_num']

        print(f"🎬 生成阶段 {stage} 动态演示: {steps}步")

        # 创建环境
        env = AGVWarehouseEnv(
            config_path="configs/environment_config.yaml",
            curriculum_stage=stage,
            render_mode=None
        )

        # 重置环境
        obs, info = env.reset(seed=42)

        # 记录运行轨迹
        trajectory = []
        for step in range(steps):
            # 随机动作（实际应用中会使用训练好的策略）
            actions = [np.random.randint(0, 5) for _ in range(agv_num)]
            obs, reward, terminated, truncated, info = env.step(actions)

            # 记录当前状态
            state = {
                'step': step,
                'agvs': [(agv.x, agv.y, agv.current_load) for agv in env.agvs],
                'tasks': [(task.x, task.y, task.state, task.weight) for task in env.tasks],
                'reward': reward,
                'terminated': terminated
            }
            trajectory.append(state)

            if terminated or truncated:
                print(f"  环境在第{step}步结束")
                break

        # 创建动画
        fig, ax = plt.subplots(figsize=self.fig_size)

        def animate(frame):
            ax.clear()

            # 绘制地图
            self._draw_map(ax, env)

            # 获取当前帧状态
            state = trajectory[frame]

            # 绘制AGV
            for i, (x, y, load) in enumerate(state['agvs']):
                if i < len(self.colors['agv']):
                    color = self.colors['agv'][i]
                else:
                    color = self.colors['agv'][i % len(self.colors['agv'])]

                circle = patches.Circle((x + 0.5, y + 0.5), 0.3,
                                      facecolor=color, edgecolor='black', linewidth=2)
                ax.add_patch(circle)
                ax.text(x + 0.5, y + 0.5, f'A{i+1}',
                       ha='center', va='center', fontweight='bold', fontsize=10)
                ax.text(x + 0.5, y - 0.2, f'{load}',
                       ha='center', va='center', fontsize=8,
                       bbox=dict(boxstyle="round,pad=0.1", facecolor='white', alpha=0.8))

            # 绘制任务
            for i, (px, py, task_state, weight) in enumerate(state['tasks']):
                if task_state == 0:  # 未分配
                    rect = patches.Rectangle((px, py), 1, 1,
                                           linewidth=2, edgecolor='red',
                                           facecolor=self.colors['task'], alpha=0.7)
                    ax.add_patch(rect)
                    ax.text(px + 0.5, py + 0.5, f'T{i+1}',
                           ha='center', va='center', fontweight='bold', fontsize=9)

            # 设置标题和坐标轴
            ax.set_title(f'阶段 {stage} 动态演示 - 步数: {state["step"]}, 奖励: {state["reward"]:.2f}',
                        fontsize=14, fontweight='bold')
            ax.set_xlim(-1, env.map_generator.width)
            ax.set_ylim(-1, env.map_generator.height)
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)

        # 创建动画
        anim = FuncAnimation(fig, animate, frames=len(trajectory),
                           interval=200, repeat=True, blit=False)

        # 保存GIF
        gif_path = None
        if save_gif:
            filename = f"stage_{stage}_dynamic.gif"
            gif_path = os.path.join(self.results_dir, filename)
            anim.save(gif_path, writer='pillow', fps=5)
            print(f"  🎞️ 动态演示已保存: {gif_path}")

        env.close()
        plt.close(fig)
        return gif_path

    def visualize_all_stages(self, include_dynamic: bool = False, dynamic_steps: int = 30):
        """
        可视化所有课程学习阶段

        Args:
            include_dynamic: 是否包含动态演示
            dynamic_steps: 动态演示步数
        """
        print(f"🎨 开始可视化所有 {len(self.curriculum_stages)} 个课程学习阶段")

        results = {
            'static_images': [],
            'dynamic_gifs': [],
            'stage_info': []
        }

        for stage in sorted(self.curriculum_stages.keys()):
            print(f"\n--- 处理阶段 {stage} ---")

            try:
                # 生成静态图
                fig = self.visualize_stage_static(stage, save_fig=True)
                plt.close(fig)
                results['static_images'].append(f"stage_{stage}_static.png")

                # 生成动态演示
                if include_dynamic:
                    gif_path = self.visualize_stage_dynamic(stage, steps=dynamic_steps, save_gif=True)
                    if gif_path:
                        results['dynamic_gifs'].append(os.path.basename(gif_path))

                # 记录阶段信息
                stage_config = self.curriculum_stages[stage]
                results['stage_info'].append({
                    'stage': stage,
                    'agv_num': stage_config['agv_num'],
                    'task_num': stage_config['task_num'],
                    'max_steps': stage_config['max_steps'],
                    'episodes': stage_config['episodes']
                })

                print(f"✅ 阶段 {stage} 处理完成")

            except Exception as e:
                print(f"❌ 阶段 {stage} 处理失败: {e}")
                continue

        # 生成总结报告
        self._generate_summary_report(results)

        print(f"\n🎉 所有阶段可视化完成！")
        print(f"  静态图片: {len(results['static_images'])} 个")
        if include_dynamic:
            print(f"  动态演示: {len(results['dynamic_gifs'])} 个")
        print(f"  结果保存在: {self.results_dir}")

        return results

    def _generate_summary_report(self, results: Dict):
        """生成总结报告"""
        report_path = os.path.join(self.results_dir, "curriculum_stages_report.md")

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 课程学习阶段可视化报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## 阶段概览\n\n")
            f.write("| 阶段 | AGV数量 | 任务数量 | 最大步数 | 目标Episodes |\n")
            f.write("|------|---------|----------|----------|-------------|\n")

            for stage_info in results['stage_info']:
                f.write(f"| {stage_info['stage']} | {stage_info['agv_num']} | "
                       f"{stage_info['task_num']} | {stage_info['max_steps']} | "
                       f"{stage_info['episodes']} |\n")

            f.write("\n## 可视化文件\n\n")
            f.write("### 静态场景图\n\n")
            for img in results['static_images']:
                f.write(f"- ![阶段图片]({img})\n")

            if results['dynamic_gifs']:
                f.write("\n### 动态演示\n\n")
                for gif in results['dynamic_gifs']:
                    f.write(f"- ![动态演示]({gif})\n")

            f.write("\n## 课程学习进度分析\n\n")
            f.write("课程学习设计遵循从简单到复杂的渐进式原则：\n\n")
            f.write("1. **初级阶段 (1-3)**: 单AGV处理少量任务，学习基本操作\n")
            f.write("2. **中级阶段 (4-6)**: 双AGV协作，任务数量增加\n")
            f.write("3. **高级阶段 (7-9)**: 三AGV复杂协作场景\n")
            f.write("4. **专家阶段 (10-12)**: 四AGV最大规模协作\n\n")

            f.write("每个阶段的复杂度递增确保了智能体能够逐步学习和适应更复杂的多智能体协作任务。\n")

        print(f"📋 总结报告已生成: {report_path}")

    def validate_stage_configurations(self) -> Dict[str, Any]:
        """
        验证所有阶段配置的正确性

        Returns:
            dict: 验证结果
        """
        print("🔍 验证课程学习阶段配置...")

        validation_results = {
            'valid_stages': [],
            'invalid_stages': [],
            'warnings': [],
            'summary': {}
        }

        for stage in sorted(self.curriculum_stages.keys()):
            stage_config = self.curriculum_stages[stage]

            try:
                # 尝试创建环境
                env = AGVWarehouseEnv(
                    config_path="configs/environment_config.yaml",
                    curriculum_stage=stage,
                    render_mode=None
                )

                # 验证环境参数
                agv_num = stage_config['agv_num']
                task_num = stage_config['task_num']

                if env.num_agvs != agv_num:
                    validation_results['warnings'].append(
                        f"阶段{stage}: 配置AGV数量({agv_num}) != 环境AGV数量({env.num_agvs})")

                if env.num_tasks != task_num:
                    validation_results['warnings'].append(
                        f"阶段{stage}: 配置任务数量({task_num}) != 环境任务数量({env.num_tasks})")

                # 测试环境重置和步骤
                obs, info = env.reset(seed=42)
                actions = [0] * agv_num  # 全部等待动作
                obs, reward, terminated, truncated, info = env.step(actions)

                validation_results['valid_stages'].append(stage)
                env.close()

            except Exception as e:
                validation_results['invalid_stages'].append({
                    'stage': stage,
                    'error': str(e)
                })

        # 生成验证总结
        validation_results['summary'] = {
            'total_stages': len(self.curriculum_stages),
            'valid_count': len(validation_results['valid_stages']),
            'invalid_count': len(validation_results['invalid_stages']),
            'warning_count': len(validation_results['warnings'])
        }

        print(f"✅ 验证完成:")
        print(f"  有效阶段: {validation_results['summary']['valid_count']}")
        print(f"  无效阶段: {validation_results['summary']['invalid_count']}")
        print(f"  警告数量: {validation_results['summary']['warning_count']}")

        if validation_results['invalid_stages']:
            print("❌ 无效阶段:")
            for invalid in validation_results['invalid_stages']:
                print(f"  阶段{invalid['stage']}: {invalid['error']}")

        if validation_results['warnings']:
            print("⚠️ 警告:")
            for warning in validation_results['warnings']:
                print(f"  {warning}")

        return validation_results


if __name__ == "__main__":
    # 测试课程学习阶段可视化器
    print("🎨 测试课程学习阶段可视化器...")

    try:
        # 创建可视化器
        visualizer = CurriculumStageVisualizer()

        # 验证阶段配置
        print("\n🔍 验证阶段配置...")
        validation_results = visualizer.validate_stage_configurations()

        if validation_results['summary']['invalid_count'] > 0:
            print("❌ 存在无效阶段，请检查配置")
            exit(1)

        # 可视化前3个阶段作为示例
        print("\n🎯 可视化前3个阶段...")
        for stage in [1, 2, 3]:
            print(f"\n--- 阶段 {stage} ---")

            # 静态可视化
            fig = visualizer.visualize_stage_static(stage, save_fig=True)
            plt.show()
            plt.close(fig)

            # 动态可视化（较短演示）
            print(f"生成阶段 {stage} 动态演示...")
            gif_path = visualizer.visualize_stage_dynamic(stage, steps=20, save_gif=True)

        # 生成所有阶段的静态图（不包含动态演示以节省时间）
        print("\n🎨 生成所有阶段静态可视化...")
        results = visualizer.visualize_all_stages(include_dynamic=False)

        print(f"\n🎉 课程学习阶段可视化测试完成！")
        print(f"  验证结果: {validation_results['summary']['valid_count']}/{validation_results['summary']['total_stages']} 个阶段有效")
        print(f"  生成文件: {len(results['static_images'])} 个静态图片")
        print(f"  结果目录: {visualizer.results_dir}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()