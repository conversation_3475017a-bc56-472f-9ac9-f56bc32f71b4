#!/usr/bin/env python3
"""
从检查点恢复训练脚本：双层注意力MAPPO训练
支持从现有检查点继续训练，保持完整的数据连续性
"""

import os
import sys
import time
import json
import torch
import glob
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.mappo.curriculum_trainer import CurriculumTrainer


def find_latest_experiment():
    """查找最新的实验目录"""
    results_dir = "results"
    if not os.path.exists(results_dir):
        return None
    
    # 查找所有实验目录
    experiment_dirs = [d for d in os.listdir(results_dir) 
                      if d.startswith("dual_attention_mappo_") and 
                      os.path.isdir(os.path.join(results_dir, d))]
    
    if not experiment_dirs:
        return None
    
    # 返回最新的实验目录
    latest_experiment = sorted(experiment_dirs)[-1]
    return os.path.join(results_dir, latest_experiment)


def get_checkpoint_info(experiment_dir):
    """获取检查点信息"""
    checkpoint_dir = os.path.join(experiment_dir, "checkpoints")
    if not os.path.exists(checkpoint_dir):
        return None
    
    # 查找最新的训练状态文件
    state_files = glob.glob(os.path.join(checkpoint_dir, "training_state_*.txt"))
    if not state_files:
        return None
    
    # 解析最新状态文件
    latest_state_file = sorted(state_files)[-1]
    
    checkpoint_info = {}
    with open(latest_state_file, 'r', encoding='utf-8') as f:
        for line in f:
            if ':' in line:
                key, value = line.strip().split(':', 1)
                checkpoint_info[key.strip()] = value.strip()
    
    return {
        'experiment_dir': experiment_dir,
        'checkpoint_dir': checkpoint_dir,
        'state_file': latest_state_file,
        'stage': int(checkpoint_info.get('Stage', 1)),
        'total_episodes': int(checkpoint_info.get('Total Episodes', 0)),
        'stage_episodes': int(checkpoint_info.get('Stage Episodes', 0)),
        'checkpoint_path': checkpoint_info.get('Checkpoint Path', ''),
        'timestamp': float(checkpoint_info.get('Timestamp', time.time()))
    }


def load_training_history(experiment_dir):
    """加载训练历史记录"""
    training_history = []
    
    # 加载各阶段的训练历史
    for stage_dir in glob.glob(os.path.join(experiment_dir, "stage_*")):
        history_file = os.path.join(stage_dir, "training_history.json")
        if os.path.exists(history_file):
            try:
                with open(history_file, 'r', encoding='utf-8') as f:
                    stage_history = json.load(f)
                    training_history.extend(stage_history)
            except Exception as e:
                print(f"⚠️ 加载阶段历史失败 {stage_dir}: {e}")
    
    return sorted(training_history, key=lambda x: x.get('episode', 0))


def resume_training_from_checkpoint(experiment_dir=None):
    """从检查点恢复训练"""
    print("🔄 开始从检查点恢复训练")
    print("=" * 60)
    
    # 查找实验目录
    if experiment_dir is None:
        experiment_dir = find_latest_experiment()
        if experiment_dir is None:
            print("❌ 未找到可恢复的实验目录")
            return False
    
    print(f"📁 实验目录: {experiment_dir}")
    
    # 获取检查点信息
    checkpoint_info = get_checkpoint_info(experiment_dir)
    if checkpoint_info is None:
        print("❌ 未找到有效的检查点信息")
        return False
    
    print(f"📋 检查点信息:")
    print(f"  当前阶段: {checkpoint_info['stage']}")
    print(f"  总episodes: {checkpoint_info['total_episodes']}")
    print(f"  阶段episodes: {checkpoint_info['stage_episodes']}")
    print(f"  检查点时间: {datetime.fromtimestamp(checkpoint_info['timestamp'])}")
    print()
    
    # 验证GPU环境
    print("🔧 GPU环境检查:")
    print(f"  PyTorch版本: {torch.__version__}")
    print(f"  CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"  GPU设备: {torch.cuda.get_device_name(0)}")
    print()
    
    try:
        # 创建训练器（使用原实验名称）
        experiment_name = os.path.basename(experiment_dir)
        config_path = "configs/mappo_config.yaml"
        
        print("🎓 初始化课程学习训练器...")
        trainer = CurriculumTrainer(
            config_path=config_path,
            experiment_name=experiment_name,
            enable_visualization=True,
            results_base_dir="results"  # 确保使用相同的结果目录
        )
        
        # 手动设置结果目录为现有目录
        trainer.results_dir = experiment_dir
        
        # 加载训练历史
        print("📚 加载训练历史记录...")
        training_history = load_training_history(experiment_dir)
        trainer.training_history = training_history
        print(f"  已加载 {len(training_history)} 条训练记录")
        
        # 恢复训练状态
        print("🔄 恢复训练状态...")
        trainer.current_stage = checkpoint_info['stage']
        trainer.total_episodes = checkpoint_info['total_episodes']
        trainer.stage_episodes = checkpoint_info['stage_episodes']
        
        # 加载算法检查点
        print("🧠 加载算法检查点...")
        checkpoint_dir = checkpoint_info['checkpoint_dir']

        # 创建算法实例
        trainer.algorithm = trainer._create_algorithm(trainer.current_stage)

        # 查找最新的检查点文件
        checkpoint_files = glob.glob(os.path.join(checkpoint_dir, "checkpoint_*"))
        if checkpoint_files:
            # 使用最新的检查点
            latest_checkpoint = sorted(checkpoint_files)[-1]
            absolute_checkpoint_path = os.path.abspath(latest_checkpoint)
            print(f"  检查点路径: {absolute_checkpoint_path}")

            try:
                trainer.algorithm.restore(absolute_checkpoint_path)
                print("✅ 算法状态恢复成功")
            except Exception as e:
                print(f"⚠️ 算法检查点恢复失败: {e}")
                print("  将从当前阶段重新开始")
        else:
            print("⚠️ 算法检查点未找到，将从当前阶段重新开始")
        
        print("✅ 训练器恢复完成")
        print()
        
        # 继续训练
        print("🎯 继续执行课程学习训练...")
        print(f"  从阶段 {trainer.current_stage} 继续")
        print(f"  当前总episodes: {trainer.total_episodes}")
        print()
        
        # 记录恢复时间
        resume_start_time = time.time()
        
        # 继续训练（从当前阶段到最后阶段）
        training_result = trainer.train_curriculum(
            start_stage=trainer.current_stage,
            end_stage=12
        )
        
        # 记录完成时间
        resume_end_time = time.time()
        total_resume_time = resume_end_time - resume_start_time
        
        # 输出恢复训练结果
        print("\n" + "=" * 60)
        print("🎉 恢复训练执行完成！")
        print("=" * 60)
        print(f"📊 训练结果摘要:")
        print(f"  实验名称: {training_result['experiment_name']}")
        print(f"  恢复训练时长: {total_resume_time:.1f}秒 ({total_resume_time/3600:.2f}小时)")
        print(f"  总episodes: {training_result['total_episodes']}")
        print(f"  完成阶段: {training_result['stages_completed']}")
        print(f"  最终阶段: {training_result['final_stage']}")
        
        # 更新实验摘要
        summary_path = os.path.join(experiment_dir, "experiment_summary.json")
        experiment_summary = {
            "experiment_type": "resumed_training",
            "algorithm": "dual_attention_mappo",
            "experiment_name": experiment_name,
            "original_start_time": checkpoint_info['timestamp'],
            "resume_start_time": resume_start_time,
            "resume_end_time": resume_end_time,
            "resume_duration": total_resume_time,
            "training_result": training_result,
            "config_used": config_path,
            "resumed_from_stage": checkpoint_info['stage'],
            "resumed_from_episode": checkpoint_info['total_episodes'],
            "success": True
        }
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(experiment_summary, f, indent=2, ensure_ascii=False)
        
        print(f"  实验摘要更新至: {summary_path}")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ 恢复训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        if 'trainer' in locals():
            print("🧹 清理训练器资源...")
            trainer.cleanup()
            print("✅ 资源清理完成")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="从检查点恢复训练")
    parser.add_argument("--experiment-dir", type=str, help="指定实验目录路径")
    parser.add_argument("--list-experiments", action="store_true", help="列出可用的实验")
    
    args = parser.parse_args()
    
    if args.list_experiments:
        # 列出可用实验
        results_dir = "results"
        if os.path.exists(results_dir):
            experiments = [d for d in os.listdir(results_dir) 
                          if d.startswith("dual_attention_mappo_")]
            print("📋 可用的实验:")
            for exp in sorted(experiments):
                exp_path = os.path.join(results_dir, exp)
                checkpoint_info = get_checkpoint_info(exp_path)
                if checkpoint_info:
                    print(f"  {exp}")
                    print(f"    阶段: {checkpoint_info['stage']}")
                    print(f"    Episodes: {checkpoint_info['total_episodes']}")
                    print(f"    时间: {datetime.fromtimestamp(checkpoint_info['timestamp'])}")
                    print()
        return
    
    # 执行恢复训练
    success = resume_training_from_checkpoint(args.experiment_dir)
    exit_code = 0 if success else 1
    print(f"\n🏁 恢复训练{'成功' if success else '失败'}，退出码: {exit_code}")
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
