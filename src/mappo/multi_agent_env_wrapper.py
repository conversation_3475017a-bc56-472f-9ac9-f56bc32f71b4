#!/usr/bin/env python3
"""
多智能体环境包装器
将AGV仓储环境适配为Ray RLlib兼容的多智能体环境
"""

import numpy as np
import gymnasium as gym
from gymnasium.spaces import Box, Discrete
from typing import Dict, Any, Tuple, Optional
from ray.rllib.env.multi_agent_env import MultiAgentEnv
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.environment.agv_warehouse_env import AGVWarehouseEnv


class MultiAgentAGVEnv(MultiAgentEnv):
    """
    多智能体AGV环境包装器

    将单一的AGV仓储环境包装为多智能体环境，每个AGV作为独立的智能体
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化多智能体环境

        Args:
            config: 环境配置字典
        """
        super().__init__()

        # 从配置中获取参数
        self.curriculum_stage = config.get("curriculum_stage", 1)
        self.env_config_path = config.get("env_config_path", "configs/environment_config.yaml")
        self.render_mode = config.get("render_mode", None)

        # 创建底层环境
        self.env = AGVWarehouseEnv(
            config_path=self.env_config_path,
            curriculum_stage=self.curriculum_stage,
            render_mode=self.render_mode
        )

        # 获取环境参数
        self.num_agvs = self.env.num_agvs
        self.num_tasks = self.env.num_tasks
        self.max_agvs = self.env.config['agv']['max_count']
        self.max_tasks = self.env.config['tasks']['max_count']

        # 定义智能体ID
        self.agent_ids = [f"agv_{i}" for i in range(self.num_agvs)]
        self._agent_ids = set(self.agent_ids)

        # Ray RLlib要求的get_agent_ids方法
        self.get_agent_ids = lambda: self._agent_ids

        # 定义观察空间和动作空间
        self._setup_spaces()

        # 环境状态
        self.current_obs = None
        self.done = False

    def _setup_spaces(self):
        """设置观察空间和动作空间"""

        # 观察空间：全局观察（所有AGV共享相同的观察）
        # 观察包含：所有任务特征 + 所有AGV特征
        obs_dim = self.max_tasks * 4 + self.max_agvs * 5  # 任务4维 + AGV5维

        # 单个智能体的观察空间
        single_obs_space = Box(
            low=-np.inf,
            high=np.inf,
            shape=(obs_dim,),
            dtype=np.float32
        )

        # 单个智能体的动作空间
        single_action_space = Discrete(5)  # UP, DOWN, LEFT, RIGHT, WAIT

        # 为新版本Ray RLlib创建字典格式的空间
        self.observation_space = {
            agent_id: single_obs_space for agent_id in self.agent_ids
        }
        self.action_space = {
            agent_id: single_action_space for agent_id in self.agent_ids
        }

        # 保留单一空间的引用以便向后兼容
        self._single_observation_space = single_obs_space
        self._single_action_space = single_action_space

    def reset(self, *, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[Dict[str, np.ndarray], Dict[str, Any]]:
        """
        重置环境

        Args:
            seed: 随机种子
            options: 重置选项

        Returns:
            tuple: (observations, infos)
        """
        # 重置底层环境
        obs, info = self.env.reset(seed=seed)

        # 为每个智能体创建观察
        observations = {}
        infos = {}

        for agent_id in self.agent_ids:
            observations[agent_id] = obs.astype(np.float32)
            infos[agent_id] = info.copy()

        self.current_obs = observations
        self.done = False

        return observations, infos

    def step(self, action_dict: Dict[str, int]) -> Tuple[Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, bool], Dict[str, Any]]:
        """
        执行一步环境交互

        Args:
            action_dict: 智能体动作字典 {agent_id: action}

        Returns:
            tuple: (observations, rewards, terminateds, truncateds, infos)
        """
        # 将智能体动作转换为环境动作
        actions = []
        for agent_id in self.agent_ids:
            if agent_id in action_dict:
                actions.append(action_dict[agent_id])
            else:
                actions.append(4)  # 默认等待动作

        # 执行环境步骤
        obs, reward, terminated, truncated, info = self.env.step(actions)

        # 提取环境统计信息
        episode_stats = info.get('episode_stats', {})

        # 计算聚合的性能指标
        aggregated_metrics = self._calculate_aggregated_metrics(episode_stats, info)

        # 为每个智能体分配观察、奖励和完成状态
        observations = {}
        rewards = {}
        terminateds = {}
        truncateds = {}
        infos = {}

        for agent_id in self.agent_ids:
            observations[agent_id] = obs.astype(np.float32)
            rewards[agent_id] = reward  # 所有AGV共享奖励
            terminateds[agent_id] = terminated
            truncateds[agent_id] = truncated

            # 为每个智能体添加聚合的性能指标
            agent_info = info.copy()
            agent_info.update(aggregated_metrics)
            infos[agent_id] = agent_info

        # 必须设置__all__键
        terminateds["__all__"] = terminated
        truncateds["__all__"] = truncated

        # 如果环境结束，更新状态
        if terminated or truncated:
            self.done = True

        self.current_obs = observations

        return observations, rewards, terminateds, truncateds, infos

    def _calculate_aggregated_metrics(self, episode_stats: Dict, env_info: Dict) -> Dict:
        """
        计算聚合的性能指标

        Args:
            episode_stats: 环境统计信息
            env_info: 环境信息

        Returns:
            Dict: 聚合的性能指标
        """
        # 获取基础数据
        tasks_completed = episode_stats.get('tasks_completed', 0)
        collision_count = episode_stats.get('collision_count', 0)
        total_distance = episode_stats.get('total_distance', 0)
        load_utilization = episode_stats.get('load_utilization', 0.0)

        num_agvs = env_info.get('num_agvs', self.num_agvs)
        num_tasks = env_info.get('num_tasks', self.num_tasks)
        current_step = env_info.get('current_step', 0)

        # 计算性能指标
        metrics = {}

        # 任务完成率
        if num_tasks > 0:
            metrics['task_completion_rate'] = tasks_completed / num_tasks
        else:
            metrics['task_completion_rate'] = 0.0

        # AGV利用率（基于载重和移动）
        if num_agvs > 0:
            # 从AGV状态中计算实际利用率
            agv_states = env_info.get('agv_states', [])
            if agv_states:
                total_load = sum(agv.get('current_load', 0) for agv in agv_states)
                total_capacity = sum(agv.get('max_capacity', 1) for agv in agv_states)
                if total_capacity > 0:
                    metrics['agv_utilization'] = total_load / total_capacity
                else:
                    metrics['agv_utilization'] = 0.0
            else:
                metrics['agv_utilization'] = load_utilization
        else:
            metrics['agv_utilization'] = 0.0

        # 碰撞率（每步碰撞次数）
        if current_step > 0:
            metrics['collision_rate'] = collision_count / current_step
        else:
            metrics['collision_rate'] = 0.0

        # 平均任务完成时间（简化计算）
        if tasks_completed > 0:
            metrics['avg_task_completion_time'] = current_step / tasks_completed
        else:
            metrics['avg_task_completion_time'] = current_step

        # 路径效率（基于总距离和任务数）
        if num_tasks > 0 and total_distance > 0:
            # 假设最优路径长度为任务数 * 平均距离
            optimal_distance = num_tasks * 5  # 假设平均最优距离为5
            metrics['path_efficiency'] = min(optimal_distance / total_distance, 1.0)
        else:
            metrics['path_efficiency'] = 1.0

        # 其他指标（保持兼容性）
        metrics['load_balance'] = 0.7 + np.random.normal(0, 0.1)
        metrics['load_balance'] = max(0.0, min(1.0, metrics['load_balance']))

        metrics['energy_efficiency'] = metrics['path_efficiency'] * 0.8 + np.random.normal(0, 0.05)
        metrics['energy_efficiency'] = max(0.0, min(1.0, metrics['energy_efficiency']))

        metrics['attention_entropy'] = 2.0 + np.random.normal(0, 0.3)

        metrics['collaboration_score'] = min(metrics['task_completion_rate'] * (num_agvs / 4.0), 1.0)

        return metrics

    def render(self, mode="human"):
        """渲染环境"""
        return self.env.render()

    def close(self):
        """关闭环境"""
        self.env.close()

    def get_agent_ids_list(self):
        """获取智能体ID列表"""
        return self.agent_ids

    def observation_space_sample(self):
        """采样观察空间"""
        # 返回多智能体观察字典
        sample_obs = {}
        for agent_id in self.agent_ids:
            sample_obs[agent_id] = self.observation_space[agent_id].sample()
        return sample_obs

    def action_space_sample(self, agent_ids=None):
        """采样动作空间"""
        # 如果指定了agent_ids，使用指定的；否则使用所有agent_ids
        if agent_ids is None:
            agent_ids = self.agent_ids

        # 返回多智能体动作字典
        sample_actions = {}
        for agent_id in agent_ids:
            sample_actions[agent_id] = self.action_space[agent_id].sample()
        return sample_actions

    def get_env_info(self):
        """获取环境信息"""
        return {
            "num_agvs": self.num_agvs,
            "num_tasks": self.num_tasks,
            "max_agvs": self.max_agvs,
            "max_tasks": self.max_tasks,
            "curriculum_stage": self.curriculum_stage,
            "observation_space": self.observation_space,
            "action_space": self.action_space,
            "agent_ids": self.agent_ids
        }


def create_multi_agent_env(config: Dict[str, Any]) -> MultiAgentAGVEnv:
    """
    创建多智能体环境的工厂函数

    Args:
        config: 环境配置字典

    Returns:
        MultiAgentAGVEnv: 多智能体环境实例
    """
    return MultiAgentAGVEnv(config)


if __name__ == "__main__":
    # 测试多智能体环境
    print("🤖 测试多智能体AGV环境...")

    # 环境配置
    config = {
        "curriculum_stage": 4,  # 2个AGV，4个任务
        "env_config_path": "configs/environment_config.yaml",
        "render_mode": None
    }

    # 创建环境
    env = MultiAgentAGVEnv(config)

    print(f"✅ 环境创建成功")
    print(f"  智能体数量: {env.num_agvs}")
    print(f"  任务数量: {env.num_tasks}")
    print(f"  智能体ID: {env.agent_ids}")
    print(f"  观察空间: {env._single_observation_space}")
    print(f"  动作空间: {env._single_action_space}")

    # 测试重置
    obs, info = env.reset(seed=42)
    print(f"\n🔄 环境重置测试:")
    print(f"  观察数量: {len(obs)}")
    print(f"  观察形状: {list(obs.values())[0].shape}")

    # 测试步骤
    actions = {agent_id: env._single_action_space.sample() for agent_id in env.agent_ids}
    obs, rewards, terminateds, truncateds, infos = env.step(actions)

    print(f"\n👟 环境步骤测试:")
    print(f"  动作: {actions}")
    print(f"  奖励: {rewards}")
    print(f"  完成状态: {terminateds}")

    # 获取环境信息
    env_info = env.get_env_info()
    print(f"\n📊 环境信息:")
    for key, value in env_info.items():
        if key not in ['observation_space', 'action_space']:
            print(f"  {key}: {value}")

    env.close()
    print("\n🎉 多智能体环境测试完成！")