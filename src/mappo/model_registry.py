#!/usr/bin/env python3
"""
Ray RLlib模型注册器
用于注册自定义的注意力网络模型
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from ray.rllib.models import ModelCatalog
from src.mappo.attention_policy_network import AttentionPolicyNetwork
from src.mappo.attention_value_network import AttentionValueNetwork


def register_attention_models():
    """
    注册注意力机制相关的自定义模型到Ray RLlib

    注册的模型：
    - "attention_policy": 集成双层注意力的策略网络
    - "attention_value": 集成双层注意力的价值网络
    """

    # 注册策略网络
    ModelCatalog.register_custom_model(
        "attention_policy",
        AttentionPolicyNetwork
    )

    # 注册价值网络
    ModelCatalog.register_custom_model(
        "attention_value",
        AttentionValueNetwork
    )

    print("✅ 注意力模型注册完成:")
    print("  - attention_policy: 双层注意力策略网络")
    print("  - attention_value: 双层注意力价值网络")


def get_model_config(max_agvs=4, max_tasks=16, attention_config_path="configs/attention_config.yaml"):
    """
    获取注意力模型的配置

    Args:
        max_agvs: 最大AGV数量
        max_tasks: 最大任务数量
        attention_config_path: 注意力配置文件路径

    Returns:
        dict: 模型配置字典
    """
    return {
        "custom_model": "attention_policy",
        "custom_model_config": {
            "max_agvs": max_agvs,
            "max_tasks": max_tasks,
            "agv_feature_dim": 5,
            "task_feature_dim": 4,
            "attention_config_path": attention_config_path
        }
    }


def get_value_model_config(max_agvs=4, max_tasks=16, attention_config_path="configs/attention_config.yaml"):
    """
    获取注意力价值模型的配置

    Args:
        max_agvs: 最大AGV数量
        max_tasks: 最大任务数量
        attention_config_path: 注意力配置文件路径

    Returns:
        dict: 价值模型配置字典
    """
    return {
        "custom_model": "attention_value",
        "custom_model_config": {
            "max_agvs": max_agvs,
            "max_tasks": max_tasks,
            "agv_feature_dim": 5,
            "task_feature_dim": 4,
            "attention_config_path": attention_config_path
        }
    }


if __name__ == "__main__":
    # 测试模型注册
    print("🔧 测试模型注册...")

    # 注册模型
    register_attention_models()

    # 获取模型配置
    policy_config = get_model_config()
    value_config = get_value_model_config()

    print(f"\n📋 策略模型配置:")
    for key, value in policy_config.items():
        print(f"  {key}: {value}")

    print(f"\n📋 价值模型配置:")
    for key, value in value_config.items():
        print(f"  {key}: {value}")

    print("\n🎉 模型注册器测试完成！")