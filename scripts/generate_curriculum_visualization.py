#!/usr/bin/env python3
"""
生成课程学习阶段完整可视化脚本
为所有12个阶段生成静态PNG图和动态GIF图
"""

import os
import sys
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.visualization.curriculum_stage_visualizer import CurriculumStageVisualizer


def main():
    """主函数：生成所有阶段的完整可视化"""
    print("🎨 课程学习阶段完整可视化生成器")
    print("=" * 50)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 创建可视化器
        print("\n🔧 初始化可视化器...")
        visualizer = CurriculumStageVisualizer()

        # 验证阶段配置
        print("\n🔍 验证阶段配置...")
        validation_results = visualizer.validate_stage_configurations()

        if validation_results['summary']['invalid_count'] > 0:
            print("❌ 存在无效阶段，请检查配置")
            for invalid in validation_results['invalid_stages']:
                print(f"  阶段{invalid['stage']}: {invalid['error']}")
            return False

        print(f"✅ 所有 {validation_results['summary']['valid_count']} 个阶段配置有效")

        # 生成完整可视化
        print("\n🎨 开始生成完整可视化...")
        print("  📸 每个阶段将生成: 1个静态PNG图")
        print("  🎬 每个阶段将生成: 1个动态GIF图")
        print("  📋 将生成: 1个完整的总结报告")

        start_time = time.time()

        # 生成所有阶段的静态图和动态图
        results = visualizer.visualize_all_stages(
            include_dynamic=True,  # 包含动态演示
            dynamic_steps=20       # 每个动态演示20步
        )

        end_time = time.time()
        duration = end_time - start_time

        # 输出结果统计
        print("\n" + "=" * 50)
        print("🎉 可视化生成完成！")
        print(f"⏱️  总耗时: {duration:.1f}秒 ({duration/60:.1f}分钟)")
        print(f"📸 静态图片: {len(results['static_images'])} 个")
        print(f"🎬 动态演示: {len(results['dynamic_gifs'])} 个")
        print(f"📋 阶段信息: {len(results['stage_info'])} 个")
        print(f"📁 结果目录: {visualizer.results_dir}")

        # 显示文件列表
        print("\n📂 生成的文件:")
        print("  静态图片:")
        for img in results['static_images']:
            print(f"    - {img}")

        print("  动态演示:")
        for gif in results['dynamic_gifs']:
            print(f"    - {gif}")

        print("  报告文件:")
        print(f"    - curriculum_stages_report.md")

        # 显示阶段统计
        print("\n📊 阶段统计:")
        stage_groups = {
            "初级阶段 (1-3)": [s for s in results['stage_info'] if s['stage'] <= 3],
            "中级阶段 (4-6)": [s for s in results['stage_info'] if 4 <= s['stage'] <= 6],
            "高级阶段 (7-9)": [s for s in results['stage_info'] if 7 <= s['stage'] <= 9],
            "专家阶段 (10-12)": [s for s in results['stage_info'] if s['stage'] >= 10]
        }

        for group_name, stages in stage_groups.items():
            if stages:
                agv_range = f"{min(s['agv_num'] for s in stages)}-{max(s['agv_num'] for s in stages)}"
                task_range = f"{min(s['task_num'] for s in stages)}-{max(s['task_num'] for s in stages)}"
                print(f"  {group_name}: {len(stages)}个阶段, {agv_range}个AGV, {task_range}个任务")

        print(f"\n✅ 所有文件已保存到: {visualizer.results_dir}")
        print("💡 提示: 可以在文件管理器中查看生成的图片和动画")

        return True

    except Exception as e:
        print(f"\n❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎊 课程学习阶段可视化生成成功完成！")
        exit(0)
    else:
        print("\n💥 课程学习阶段可视化生成失败！")
        exit(1)