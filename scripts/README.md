# 课程学习阶段可视化脚本

## 概述

本目录包含用于生成课程学习阶段完整可视化的脚本。这些脚本可以为所有12个课程学习阶段生成静态PNG图和动态GIF图，帮助验证和展示多智能体AGV协作的渐进式学习过程。

## 脚本说明

### `generate_curriculum_visualization.py`

**功能**: 生成所有12个课程学习阶段的完整可视化

**特性**:
- ✅ 为每个阶段生成高质量静态PNG图
- ✅ 为每个阶段生成简短动态GIF演示
- ✅ 自动验证阶段配置的正确性
- ✅ 生成详细的总结报告
- ✅ 支持中文字体显示（尽管有警告但功能正常）

**使用方法**:
```bash
# 在项目根目录下运行
cd /path/to/project
python scripts/generate_curriculum_visualization.py
```

**输出结果**:
- 📁 结果目录: `results/curriculum_visualization/`
- 📸 静态图片: 12个PNG文件 (`stage_1_static.png` - `stage_12_static.png`)
- 🎬 动态演示: 12个GIF文件 (`stage_1_dynamic.gif` - `stage_12_dynamic.gif`)
- 📋 总结报告: `curriculum_stages_report.md`

## 生成的可视化内容

### 静态场景图 (PNG)
- **分辨率**: 高清300 DPI
- **内容**: AGV位置、任务分布、货架布局、阶段信息
- **格式**: 12×8英寸画布，清晰的图例和标注

### 动态演示 (GIF)
- **帧数**: 20步运行演示
- **帧率**: 3 FPS，流畅播放
- **内容**: AGV移动轨迹、任务执行过程、实时奖励显示
- **优化**: 80 DPI输出，文件大小适中

### 总结报告 (Markdown)
- **阶段概览表**: 包含AGV数量、任务数量、最大步数等信息
- **分阶段展示**: 每个阶段的静态图和动态图并列显示
- **文件列表**: 完整的生成文件清单
- **进度分析**: 课程学习设计原理说明

## 课程学习阶段设计

### 初级阶段 (1-3)
- **AGV数量**: 1个
- **任务数量**: 1-4个
- **学习目标**: 基本移动和任务执行

### 中级阶段 (4-6)
- **AGV数量**: 2个
- **任务数量**: 4-8个
- **学习目标**: 双智能体协作和任务分配

### 高级阶段 (7-9)
- **AGV数量**: 3个
- **任务数量**: 8-12个
- **学习目标**: 复杂多智能体协调

### 专家阶段 (10-12)
- **AGV数量**: 4个
- **任务数量**: 12-16个
- **学习目标**: 大规模多智能体协作优化