#!/usr/bin/env python3
"""
超参数优化器
生成不同的超参数配置并执行训练实验
"""

import os
import sys
import yaml
import shutil
import subprocess
from datetime import datetime
from itertools import product

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))


class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, base_config_path="configs/mappo_config.yaml"):
        self.base_config_path = base_config_path
        self.base_config = self._load_config(base_config_path)
        self.experiments = []
        
    def _load_config(self, config_path):
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _save_config(self, config, config_path):
        """保存配置文件"""
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    def create_hyperparameter_configs(self):
        """创建超参数配置组合"""
        
        # 定义超参数搜索空间
        hyperparameters = {
            'learning_rate': [3e-4, 1e-4, 5e-4],  # 学习率
            'train_batch_size': [1000, 2000, 4000],  # 训练批大小
            'sgd_minibatch_size': [32, 64, 128],  # SGD小批大小
            'num_sgd_iter': [5, 8, 10],  # SGD迭代次数
            'rollout_fragment_length': [50, 100, 200],  # 采样片段长度
        }
        
        # 生成所有组合（这里选择部分重要组合以减少实验数量）
        important_combinations = [
            # 基线配置
            {'learning_rate': 3e-4, 'train_batch_size': 2000, 'sgd_minibatch_size': 64, 
             'num_sgd_iter': 8, 'rollout_fragment_length': 100},
            
            # 高学习率配置
            {'learning_rate': 5e-4, 'train_batch_size': 2000, 'sgd_minibatch_size': 64, 
             'num_sgd_iter': 8, 'rollout_fragment_length': 100},
            
            # 低学习率配置
            {'learning_rate': 1e-4, 'train_batch_size': 2000, 'sgd_minibatch_size': 64, 
             'num_sgd_iter': 8, 'rollout_fragment_length': 100},
            
            # 大批量配置
            {'learning_rate': 3e-4, 'train_batch_size': 4000, 'sgd_minibatch_size': 128, 
             'num_sgd_iter': 10, 'rollout_fragment_length': 200},
            
            # 小批量配置
            {'learning_rate': 3e-4, 'train_batch_size': 1000, 'sgd_minibatch_size': 32, 
             'num_sgd_iter': 5, 'rollout_fragment_length': 50},
            
            # 保守配置（更稳定）
            {'learning_rate': 1e-4, 'train_batch_size': 1000, 'sgd_minibatch_size': 32, 
             'num_sgd_iter': 5, 'rollout_fragment_length': 50},
            
            # 激进配置（更快学习）
            {'learning_rate': 5e-4, 'train_batch_size': 4000, 'sgd_minibatch_size': 128, 
             'num_sgd_iter': 10, 'rollout_fragment_length': 200},
        ]
        
        configs = []
        for i, params in enumerate(important_combinations):
            config_name = f"config_{i+1:02d}"
            config = self._create_config_variant(params, config_name)
            configs.append((config_name, config, params))
            
        return configs
    
    def _create_config_variant(self, hyperparams, config_name):
        """创建配置变体"""
        config = self.base_config.copy()
        
        # 更新算法参数
        config['algorithm']['learning_rate'] = hyperparams['learning_rate']
        
        # 更新训练参数
        config['training']['train_batch_size'] = hyperparams['train_batch_size']
        config['training']['sgd_minibatch_size'] = hyperparams['sgd_minibatch_size']
        config['training']['num_sgd_iter'] = hyperparams['num_sgd_iter']
        config['training']['rollout_fragment_length'] = hyperparams['rollout_fragment_length']
        
        return config
    
    def run_hyperparameter_experiments(self, max_episodes=16000, start_stage=1, end_stage=12):
        """运行超参数实验"""
        
        print("🔬 开始超参数优化实验")
        print("=" * 80)
        
        # 创建超参数配置
        configs = self.create_hyperparameter_configs()
        
        print(f"📋 将执行 {len(configs)} 个超参数配置实验:")
        for i, (config_name, config, params) in enumerate(configs):
            print(f"  {i+1}. {config_name}: LR={params['learning_rate']}, "
                  f"Batch={params['train_batch_size']}, "
                  f"MiniBatch={params['sgd_minibatch_size']}, "
                  f"SGD={params['num_sgd_iter']}, "
                  f"Fragment={params['rollout_fragment_length']}")
        print()
        
        results = []
        
        for i, (config_name, config, params) in enumerate(configs):
            print(f"\n🧪 执行实验 {i+1}/{len(configs)}: {config_name}")
            print("-" * 60)
            
            # 保存配置文件
            config_path = f"configs/hyperopt_{config_name}.yaml"
            self._save_config(config, config_path)
            
            # 创建实验名称
            experiment_suffix = f"{config_name}_lr{params['learning_rate']}_batch{params['train_batch_size']}"
            
            try:
                # 执行训练实验
                cmd = [
                    "python", "scripts/hyperparameter_training.py",
                    "--config", config_path,
                    "--episodes", str(max_episodes),
                    "--suffix", experiment_suffix,
                    "--start-stage", str(start_stage),
                    "--end-stage", str(end_stage)
                ]
                
                print(f"执行命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
                
                if result.returncode == 0:
                    print(f"✅ 实验 {config_name} 成功完成")
                    results.append({
                        'config_name': config_name,
                        'params': params,
                        'success': True,
                        'stdout': result.stdout,
                        'stderr': result.stderr
                    })
                else:
                    print(f"❌ 实验 {config_name} 失败")
                    print(f"错误输出: {result.stderr}")
                    results.append({
                        'config_name': config_name,
                        'params': params,
                        'success': False,
                        'stdout': result.stdout,
                        'stderr': result.stderr
                    })
                    
            except Exception as e:
                print(f"❌ 实验 {config_name} 执行异常: {e}")
                results.append({
                    'config_name': config_name,
                    'params': params,
                    'success': False,
                    'error': str(e)
                })
        
        # 保存实验结果摘要
        self._save_experiment_summary(results)
        
        return results
    
    def _save_experiment_summary(self, results):
        """保存实验结果摘要"""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_experiments': len(results),
            'successful_experiments': sum(1 for r in results if r['success']),
            'failed_experiments': sum(1 for r in results if not r['success']),
            'results': results
        }
        
        summary_path = f"results/hyperparameter_optimization_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(summary_path), exist_ok=True)
        
        import json
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 实验摘要保存至: {summary_path}")
        print(f"成功实验: {summary['successful_experiments']}/{summary['total_experiments']}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='超参数优化器')
    parser.add_argument('--episodes', type=int, default=16000,
                       help='每个实验的最大训练episodes数')
    parser.add_argument('--start-stage', type=int, default=1,
                       help='开始阶段')
    parser.add_argument('--end-stage', type=int, default=12,
                       help='结束阶段')
    
    args = parser.parse_args()
    
    # 创建优化器并运行实验
    optimizer = HyperparameterOptimizer()
    results = optimizer.run_hyperparameter_experiments(
        max_episodes=args.episodes,
        start_stage=args.start_stage,
        end_stage=args.end_stage
    )
    
    print("\n🎉 超参数优化实验完成！")


if __name__ == "__main__":
    main()
