"""
A*路径规划算法实现
用于AGV的路径规划和导航
"""

import heapq
import numpy as np
from typing import List, Tuple, Optional, Set
from dataclasses import dataclass, field

@dataclass
class Node:
    """A*算法节点"""
    x: int
    y: int
    g_cost: float = 0.0      # 从起点到当前节点的实际代价
    h_cost: float = 0.0      # 从当前节点到终点的启发式代价
    parent: Optional['Node'] = None
    
    @property
    def f_cost(self) -> float:
        """总代价 f = g + h"""
        return self.g_cost + self.h_cost
    
    def __lt__(self, other: 'Node') -> bool:
        """用于优先队列排序"""
        return self.f_cost < other.f_cost
    
    def __eq__(self, other: 'Node') -> bool:
        """节点相等比较"""
        return self.x == other.x and self.y == other.y
    
    def __hash__(self) -> int:
        """用于集合操作"""
        return hash((self.x, self.y))

class AStarPathfinder:
    """A*路径规划器"""
    
    def __init__(self, map_grid: np.ndarray):
        """
        初始化路径规划器
        
        Args:
            map_grid: 地图矩阵
        """
        self.map_grid = map_grid
        self.height, self.width = map_grid.shape
        
        # 可通行的地图元素
        self.walkable_tiles = {0, 3, 4}  # EMPTY, START, END
        
        # 移动方向 (dx, dy)
        self.directions = [
            (0, -1),  # UP
            (0, 1),   # DOWN
            (-1, 0),  # LEFT
            (1, 0),   # RIGHT
        ]
    
    def heuristic(self, node: Node, goal: Node) -> float:
        """
        启发式函数 - 曼哈顿距离
        
        Args:
            node: 当前节点
            goal: 目标节点
            
        Returns:
            float: 启发式距离
        """
        return abs(node.x - goal.x) + abs(node.y - goal.y)
    
    def is_valid_position(self, x: int, y: int, occupied_positions: Set[Tuple[int, int]] = None) -> bool:
        """
        检查位置是否有效
        
        Args:
            x, y: 位置坐标
            occupied_positions: 被其他AGV占用的位置
            
        Returns:
            bool: 是否有效
        """
        # 检查边界
        if not (0 <= x < self.width and 0 <= y < self.height):
            return False
        
        # 检查地图元素
        if self.map_grid[y, x] not in self.walkable_tiles:
            return False
        
        # 检查是否被其他AGV占用
        if occupied_positions and (x, y) in occupied_positions:
            return False
        
        return True
    
    def get_neighbors(self, node: Node, occupied_positions: Set[Tuple[int, int]] = None) -> List[Node]:
        """
        获取节点的邻居节点
        
        Args:
            node: 当前节点
            occupied_positions: 被占用的位置
            
        Returns:
            List[Node]: 邻居节点列表
        """
        neighbors = []
        
        for dx, dy in self.directions:
            new_x = node.x + dx
            new_y = node.y + dy
            
            if self.is_valid_position(new_x, new_y, occupied_positions):
                neighbors.append(Node(new_x, new_y))
        
        return neighbors
    
    def find_path(self, start: Tuple[int, int], goal: Tuple[int, int], 
                  occupied_positions: Set[Tuple[int, int]] = None) -> Optional[List[Tuple[int, int]]]:
        """
        使用A*算法寻找路径
        
        Args:
            start: 起始位置 (x, y)
            goal: 目标位置 (x, y)
            occupied_positions: 被其他AGV占用的位置
            
        Returns:
            Optional[List[Tuple[int, int]]]: 路径点列表，如果无路径则返回None
        """
        start_node = Node(start[0], start[1])
        goal_node = Node(goal[0], goal[1])
        
        # 检查起点和终点是否有效
        if not self.is_valid_position(start[0], start[1], occupied_positions):
            return None
        if not self.is_valid_position(goal[0], goal[1], occupied_positions):
            return None
        
        # 如果起点就是终点
        if start == goal:
            return [start]
        
        # 开放列表和关闭列表
        open_list = []
        closed_set = set()
        
        # 添加起始节点到开放列表
        heapq.heappush(open_list, start_node)
        
        # 用于快速查找开放列表中的节点
        open_dict = {(start_node.x, start_node.y): start_node}
        
        while open_list:
            # 获取f_cost最小的节点
            current = heapq.heappop(open_list)
            current_pos = (current.x, current.y)
            
            # 从开放字典中移除
            if current_pos in open_dict:
                del open_dict[current_pos]
            
            # 添加到关闭列表
            closed_set.add(current_pos)
            
            # 检查是否到达目标
            if current.x == goal_node.x and current.y == goal_node.y:
                return self._reconstruct_path(current)
            
            # 检查所有邻居
            for neighbor in self.get_neighbors(current, occupied_positions):
                neighbor_pos = (neighbor.x, neighbor.y)
                
                # 跳过已在关闭列表中的节点
                if neighbor_pos in closed_set:
                    continue
                
                # 计算新的g_cost
                tentative_g = current.g_cost + 1.0  # 移动代价为1
                
                # 检查是否在开放列表中
                if neighbor_pos in open_dict:
                    existing_neighbor = open_dict[neighbor_pos]
                    if tentative_g < existing_neighbor.g_cost:
                        # 找到更好的路径
                        existing_neighbor.g_cost = tentative_g
                        existing_neighbor.parent = current
                else:
                    # 新节点，添加到开放列表
                    neighbor.g_cost = tentative_g
                    neighbor.h_cost = self.heuristic(neighbor, goal_node)
                    neighbor.parent = current
                    
                    heapq.heappush(open_list, neighbor)
                    open_dict[neighbor_pos] = neighbor
        
        # 没有找到路径
        return None
    
    def _reconstruct_path(self, node: Node) -> List[Tuple[int, int]]:
        """
        重构路径
        
        Args:
            node: 目标节点
            
        Returns:
            List[Tuple[int, int]]: 路径点列表
        """
        path = []
        current = node
        
        while current is not None:
            path.append((current.x, current.y))
            current = current.parent
        
        # 反转路径（从起点到终点）
        path.reverse()
        return path
    
    def get_next_action(self, current_pos: Tuple[int, int], target_pos: Tuple[int, int]) -> Optional[int]:
        """
        根据当前位置和目标位置获取下一步动作
        
        Args:
            current_pos: 当前位置 (x, y)
            target_pos: 目标位置 (x, y)
            
        Returns:
            Optional[int]: 动作编号，如果无法移动则返回None
        """
        dx = target_pos[0] - current_pos[0]
        dy = target_pos[1] - current_pos[1]
        
        if dx == 0 and dy == -1:
            return 0  # UP
        elif dx == 0 and dy == 1:
            return 1  # DOWN
        elif dx == -1 and dy == 0:
            return 2  # LEFT
        elif dx == 1 and dy == 0:
            return 3  # RIGHT
        else:
            return 4  # WAIT or invalid move

if __name__ == "__main__":
    # 测试A*路径规划
    print("🗺️  测试A*路径规划...")
    
    # 创建简单测试地图
    test_map = np.array([
        [0, 0, 0, 2, 0],
        [0, 2, 0, 2, 0],
        [0, 2, 0, 0, 0],
        [0, 0, 0, 2, 0],
        [0, 0, 0, 0, 0]
    ])
    
    pathfinder = AStarPathfinder(test_map)
    
    # 测试路径规划
    start = (0, 0)
    goal = (4, 4)
    
    path = pathfinder.find_path(start, goal)
    
    if path:
        print(f"找到路径: {path}")
        print(f"路径长度: {len(path)}")
        
        # 测试动作序列
        actions = []
        for i in range(len(path) - 1):
            action = pathfinder.get_next_action(path[i], path[i + 1])
            actions.append(action)
        
        print(f"动作序列: {actions}")
    else:
        print("未找到路径")
    
    print("✅ A*路径规划测试完成！")
