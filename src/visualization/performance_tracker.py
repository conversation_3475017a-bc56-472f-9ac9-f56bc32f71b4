#!/usr/bin/env python3
"""
性能指标动态跟踪器
实现多维度性能指标的动态监控和分析
"""

import os
import time
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import seaborn as sns
from collections import deque, defaultdict

# 导入字体管理器
from .font_manager import configure_chinese_font

# 配置中文字体
configure_chinese_font()

class PerformanceTracker:
    """性能指标动态跟踪器"""
    
    def __init__(self, save_dir: str = "results/performance_tracker",
                 update_interval: int = 20):
        """
        初始化性能跟踪器

        Args:
            save_dir: 保存目录
            update_interval: 更新间隔（episodes）
        """
        self.save_dir = save_dir
        self.update_interval = update_interval

        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # AGV状态指标历史 - 重点关注4个核心指标
        self.agv_metrics = {
            'episodes': deque(maxlen=10000),
            'stages': deque(maxlen=10000),
            'agv_utilization': deque(maxlen=10000),      # AGV载重利用率
            'task_completion_rate': deque(maxlen=10000), # 任务完成率
            'path_length': deque(maxlen=10000),          # 路径长度
            'collision_count': deque(maxlen=10000),      # 碰撞次数
            'timestamps': deque(maxlen=10000)
        }
        
        # 当前状态
        self.current_stage = 1
        self.total_episodes = 0
        self.last_update_episode = 0

        # 图表配置
        self.fig_size = (16, 12)
        self.colors = {
            'agv_utilization': '#2E86AB',      # 蓝色
            'task_completion': '#28A745',      # 绿色
            'path_length': '#F18F01',          # 橙色
            'collision_count': '#DC3545'       # 红色
        }
        
        print(f"📊 性能跟踪器初始化完成")
        print(f"  保存目录: {save_dir}")
        print(f"  更新间隔: {update_interval} episodes")

    def update_save_dir(self, new_save_dir: str):
        """
        更新保存目录

        Args:
            new_save_dir: 新的保存目录路径
        """
        self.save_dir = new_save_dir
        os.makedirs(new_save_dir, exist_ok=True)
        print(f"📁 性能跟踪器保存目录已更新: {new_save_dir}")
    
    def record_performance(self, episode: int, stage: int,
                          performance_data: Dict[str, float]) -> None:
        """
        记录AGV状态指标

        Args:
            episode: episode编号
            stage: 当前阶段
            performance_data: 性能数据字典
        """
        # 更新当前状态
        if stage != self.current_stage:
            self.current_stage = stage

        # 记录基础信息
        self.agv_metrics['episodes'].append(episode)
        self.agv_metrics['stages'].append(stage)
        self.agv_metrics['timestamps'].append(time.time())

        # 提取4个核心AGV状态指标
        agv_utilization = performance_data.get('agv_utilization', 0.0)
        task_completion_rate = performance_data.get('task_completion_rate', 0.0)

        # 从episode长度计算路径长度（实际应该从环境获取）
        episode_length = performance_data.get('avg_task_completion_time', 0.0)
        path_length = episode_length  # 简化处理，实际应该是累计路径长度

        # 从碰撞率计算碰撞次数（实际应该从环境获取）
        collision_rate = performance_data.get('collision_rate', 0.0)
        # 修复：将碰撞率转换为整数碰撞次数
        if collision_rate > 0:
            collision_count = max(1, int(collision_rate * 100))  # 将小数率转换为整数次数
        else:
            collision_count = 0

        # 记录AGV状态指标
        self.agv_metrics['agv_utilization'].append(agv_utilization)
        self.agv_metrics['task_completion_rate'].append(task_completion_rate)
        self.agv_metrics['path_length'].append(path_length)
        self.agv_metrics['collision_count'].append(collision_count)

        self.total_episodes = episode

        # 检查是否需要更新图表
        if episode - self.last_update_episode >= self.update_interval:
            self.update_agv_status_plot()
            self.last_update_episode = episode

    def update_agv_status_plot(self) -> None:
        """更新AGV状态图表 - 每个阶段只生成一张固定文件名的图片"""
        if len(self.agv_metrics['episodes']) < 2:
            return

        try:
            # 只创建一张AGV状态图，包含当前阶段的所有数据
            self._create_stage_agv_status()

        except Exception as e:
            print(f"⚠️ 更新AGV状态图表时出错: {e}")
            # 确保图表被关闭
            try:
                plt.close('all')
            except:
                pass

    def _create_stage_agv_status(self) -> None:
        """创建阶段AGV状态图（固定文件名，动态更新）"""
        # 获取当前阶段的数据
        current_stage_data = self._get_current_stage_agv_data()

        if not current_stage_data['episodes']:
            return

        # 创建2x2子图布局
        fig, axes = plt.subplots(2, 2, figsize=self.fig_size)
        fig.suptitle(f'阶段{self.current_stage}AGV状态指标 - Episode {self.total_episodes}',
                    fontsize=16, fontweight='bold')

        # 1. AGV载重利用率散点图和拟合曲线
        self._plot_agv_metric_scatter_fit(axes[0, 0], current_stage_data, 'agv_utilization',
                                         'AGV载重利用率', self.colors['agv_utilization'])

        # 2. 任务完成率散点图和拟合曲线
        self._plot_agv_metric_scatter_fit(axes[0, 1], current_stage_data, 'task_completion_rate',
                                         '任务完成率', self.colors['task_completion'])

        # 3. 路径长度散点图和拟合曲线
        self._plot_agv_metric_scatter_fit(axes[1, 0], current_stage_data, 'path_length',
                                         '路径长度', self.colors['path_length'])

        # 4. 碰撞次数散点图和拟合曲线
        self._plot_agv_metric_scatter_fit(axes[1, 1], current_stage_data, 'collision_count',
                                         '碰撞次数', self.colors['collision_count'])

        # 调整布局
        plt.tight_layout()

        # 保存到固定文件名（覆盖之前的文件）
        save_path = os.path.join(self.save_dir, "agv_status.png")
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig)

        print(f"📊 阶段{self.current_stage}AGV状态图已更新: {save_path}")

    def _get_current_stage_agv_data(self) -> Dict[str, List]:
        """获取当前阶段的AGV状态数据"""
        current_stage_data = {
            'episodes': [],
            'agv_utilization': [],
            'task_completion_rate': [],
            'path_length': [],
            'collision_count': []
        }

        # 从历史数据中筛选当前阶段的数据
        episodes = list(self.agv_metrics['episodes'])
        stages = list(self.agv_metrics['stages'])

        for i, stage in enumerate(stages):
            if stage == self.current_stage:
                current_stage_data['episodes'].append(episodes[i])
                current_stage_data['agv_utilization'].append(self.agv_metrics['agv_utilization'][i])
                current_stage_data['task_completion_rate'].append(self.agv_metrics['task_completion_rate'][i])
                current_stage_data['path_length'].append(self.agv_metrics['path_length'][i])
                current_stage_data['collision_count'].append(self.agv_metrics['collision_count'][i])

        return current_stage_data

    def _plot_agv_metric_scatter_fit(self, ax, stage_data: Dict, metric_key: str,
                                    metric_name: str, color: str):
        """绘制AGV指标的散点图和smooth拟合曲线"""
        episodes = np.array(stage_data['episodes'])
        values = np.array(stage_data[metric_key])

        if len(episodes) < 2:
            ax.text(0.5, 0.5, f'数据不足\n({len(episodes)} episodes)',
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=12, color='gray')
            ax.set_title(f'{metric_name}', fontweight='bold')
            return

        # 散点图
        ax.scatter(episodes, values, alpha=0.7, s=30, color=color,
                  label=f'{metric_name}数据点', zorder=3)

        # Smooth曲线拟合
        if len(episodes) >= 5:
            try:
                # 使用LOWESS平滑
                from scipy.signal import savgol_filter
                if len(values) >= 5:
                    # 确保窗口长度为奇数且不超过数据长度
                    window_length = min(max(5, len(values) // 3), len(values))
                    if window_length % 2 == 0:
                        window_length -= 1

                    smooth_values = savgol_filter(values, window_length, 3)
                    ax.plot(episodes, smooth_values, color=color, linewidth=3,
                           label=f'{metric_name}趋势', alpha=0.8, zorder=4)
            except Exception:
                # 如果LOWESS失败，使用移动平均
                window_size = min(max(3, len(values) // 5), len(values))
                if window_size >= 3:
                    moving_avg = np.convolve(values, np.ones(window_size)/window_size, mode='valid')
                    moving_episodes = episodes[window_size-1:]
                    ax.plot(moving_episodes, moving_avg, color=color, linewidth=3,
                           label=f'{metric_name}趋势', alpha=0.8, zorder=4)

        # 设置图表属性
        ax.set_title(f'{metric_name}', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel(metric_name)
        ax.grid(True, alpha=0.3)
        ax.legend()

        # 添加统计信息
        if len(values) > 0:
            mean_val = np.mean(values)
            std_val = np.std(values)
            latest_val = values[-1]

            stats_text = f'最新: {latest_val:.3f}\n均值: {mean_val:.3f}\n标准差: {std_val:.3f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                   verticalalignment='top', fontsize=9,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

    def _calculate_efficiency_score(self, performance_data: Dict[str, float]) -> float:
        """计算综合效率分数"""
        # 权重配置
        weights = {
            'task_completion_rate': 0.3,
            'agv_utilization': 0.2,
            'path_efficiency': 0.2,
            'load_balance': 0.15,
            'energy_efficiency': 0.15
        }
        
        score = 0.0
        for metric, weight in weights.items():
            value = performance_data.get(metric, 0.0)
            # 碰撞率是负向指标，需要反转
            if metric == 'collision_rate':
                value = 1.0 - min(value, 1.0)
            score += weight * value
        
        return score
    
    def generate_performance_dashboard(self) -> Optional[str]:
        """生成性能监控仪表板"""
        if len(self.performance_metrics['episodes']) < 2:
            print("⚠️  数据不足，无法生成性能仪表板")
            return None

        try:
            # 创建仪表板
            fig = plt.figure(figsize=self.fig_size)
            gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)

            # 1. 任务完成率趋势
            ax1 = fig.add_subplot(gs[0, 0])
            self._plot_task_completion_trend(ax1)

            # 2. AGV利用率分析
            ax2 = fig.add_subplot(gs[0, 1])
            self._plot_agv_utilization(ax2)

            # 3. 路径效率分析
            ax3 = fig.add_subplot(gs[0, 2])
            self._plot_path_efficiency(ax3)

            # 4. 负载均衡分析
            ax4 = fig.add_subplot(gs[1, 0])
            self._plot_load_balance(ax4)

            # 5. 协作效果分析
            ax5 = fig.add_subplot(gs[1, 1])
            self._plot_collaboration_analysis(ax5)

            # 6. 注意力机制分析
            ax6 = fig.add_subplot(gs[1, 2])
            self._plot_attention_analysis(ax6)

            # 7. 阶段性能对比
            ax7 = fig.add_subplot(gs[2, :2])
            self._plot_stage_comparison(ax7)

            # 8. 综合性能雷达图
            ax8 = fig.add_subplot(gs[2, 2], projection='polar')
            self._plot_performance_radar(ax8)

            # 设置总标题
            fig.suptitle('性能监控仪表板', fontsize=18, fontweight='bold')

            # 保存仪表板
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            save_path = os.path.join(self.save_dir, f'performance_dashboard_{timestamp}.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close(fig)  # 明确关闭图表

            print(f"📊 性能仪表板已生成: {save_path}")
            return save_path

        except Exception as e:
            print(f"❌ 生成性能仪表板失败: {e}")
            # 确保图表被关闭
            try:
                plt.close('all')
            except:
                pass
            return None
    
    def _plot_task_completion_trend(self, ax) -> None:
        """绘制任务完成率趋势"""
        episodes = list(self.performance_metrics['episodes'])
        completion_rates = list(self.performance_metrics['task_completion_rate'])
        
        ax.plot(episodes, completion_rates, color=self.colors['completion'], 
               linewidth=2, marker='o', markersize=1)
        
        # 添加移动平均线
        if len(completion_rates) > 20:
            window = min(50, len(completion_rates) // 4)
            moving_avg = np.convolve(completion_rates, np.ones(window)/window, mode='valid')
            moving_episodes = episodes[window-1:]
            ax.plot(moving_episodes, moving_avg, color='red', linewidth=2, 
                   linestyle='--', label=f'移动平均({window})')
        
        ax.set_title('任务完成率趋势', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('完成率')
        ax.set_ylim(0, 1)
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    def _plot_agv_utilization(self, ax) -> None:
        """绘制AGV利用率分析"""
        episodes = list(self.performance_metrics['episodes'])
        utilization = list(self.performance_metrics['agv_utilization'])
        
        ax.plot(episodes, utilization, color=self.colors['utilization'], 
               linewidth=2, alpha=0.7)
        
        ax.set_title('AGV利用率', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('利用率')
        ax.set_ylim(0, 1)
        ax.grid(True, alpha=0.3)
    
    def _plot_path_efficiency(self, ax) -> None:
        """绘制路径效率分析"""
        episodes = list(self.performance_metrics['episodes'])
        efficiency = list(self.performance_metrics['path_efficiency'])
        
        ax.plot(episodes, efficiency, color=self.colors['efficiency'], 
               linewidth=2, alpha=0.7)
        
        ax.set_title('路径效率', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('效率')
        ax.grid(True, alpha=0.3)
    
    def _plot_load_balance(self, ax) -> None:
        """绘制负载均衡分析"""
        episodes = list(self.performance_metrics['episodes'])
        balance = list(self.performance_metrics['load_balance'])
        
        ax.plot(episodes, balance, color=self.colors['balance'], 
               linewidth=2, alpha=0.7)
        
        ax.set_title('负载均衡', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('均衡度')
        ax.grid(True, alpha=0.3)
    
    def _plot_collaboration_analysis(self, ax) -> None:
        """绘制协作效果分析"""
        episodes = list(self.performance_metrics['episodes'])
        collaboration = list(self.performance_metrics['collaboration_score'])
        
        ax.plot(episodes, collaboration, color=self.colors['collaboration'], 
               linewidth=2, alpha=0.7)
        
        ax.set_title('协作效果', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('协作分数')
        ax.grid(True, alpha=0.3)
    
    def _plot_attention_analysis(self, ax) -> None:
        """绘制注意力机制分析"""
        episodes = list(self.performance_metrics['episodes'])
        entropy = list(self.performance_metrics['attention_entropy'])
        
        ax.plot(episodes, entropy, color=self.colors['attention'], 
               linewidth=2, alpha=0.7)
        
        ax.set_title('注意力熵', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('熵值')
        ax.grid(True, alpha=0.3)
    
    def _plot_stage_comparison(self, ax) -> None:
        """绘制阶段性能对比"""
        stages = sorted(self.stage_performance.keys())
        if not stages:
            return
        
        metrics = ['task_completion_rate', 'agv_utilization', 'efficiency_score']
        metric_names = ['任务完成率', 'AGV利用率', '综合效率']
        
        x = np.arange(len(stages))
        width = 0.25
        
        for i, (metric, name) in enumerate(zip(metrics, metric_names)):
            values = []
            for stage in stages:
                stage_data = self.stage_performance[stage][metric]
                avg_value = np.mean(stage_data) if stage_data else 0
                values.append(avg_value)
            
            ax.bar(x + i * width, values, width, label=name, alpha=0.8)
        
        ax.set_title('阶段性能对比', fontweight='bold')
        ax.set_xlabel('阶段')
        ax.set_ylabel('性能指标')
        ax.set_xticks(x + width)
        ax.set_xticklabels([f'阶段{s}' for s in stages])
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_performance_radar(self, ax) -> None:
        """绘制综合性能雷达图"""
        if not self.performance_metrics['episodes']:
            return
        
        # 计算最近性能指标的平均值
        recent_window = min(50, len(self.performance_metrics['episodes']))
        
        metrics = [
            'task_completion_rate', 'agv_utilization', 'path_efficiency',
            'load_balance', 'energy_efficiency', 'collaboration_score'
        ]
        
        metric_names = [
            '任务完成率', 'AGV利用率', '路径效率',
            '负载均衡', '能耗效率', '协作效果'
        ]
        
        values = []
        for metric in metrics:
            recent_data = list(self.performance_metrics[metric])[-recent_window:]
            avg_value = np.mean(recent_data) if recent_data else 0
            values.append(avg_value)
        
        # 添加第一个值到末尾以闭合雷达图
        values += values[:1]
        metric_names += metric_names[:1]
        
        # 角度
        angles = np.linspace(0, 2 * np.pi, len(metric_names), endpoint=True)
        
        # 绘制雷达图
        ax.plot(angles, values, 'o-', linewidth=2, color=self.colors['completion'])
        ax.fill(angles, values, alpha=0.25, color=self.colors['completion'])
        
        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metric_names[:-1])
        ax.set_ylim(0, 1)
        ax.set_title('综合性能雷达图', fontweight='bold', pad=20)
        ax.grid(True)
    
    def generate_performance_report(self) -> str:
        """生成性能分析报告"""
        if not self.agv_metrics['episodes']:
            return "暂无性能数据"

        # 计算总体统计
        recent_window = min(100, len(self.agv_metrics['episodes']))

        report = f"""# 性能分析报告

## 总体性能概况

### 最近{recent_window}个Episodes AGV状态指标
"""
        
        metrics_info = {
            'task_completion_rate': '任务完成率',
            'agv_utilization': 'AGV利用率',
            'path_length': '路径长度',
            'collision_count': '碰撞次数'
        }

        for metric, name in metrics_info.items():
            if metric in self.agv_metrics:
                recent_data = list(self.agv_metrics[metric])[-recent_window:]
                if recent_data:
                    avg_value = np.mean(recent_data)
                    std_value = np.std(recent_data)
                    report += f"- **{name}**: {avg_value:.3f} ± {std_value:.3f}\n"

        # 当前阶段分析
        report += f"\n## 当前阶段分析\n"
        report += f"- 当前阶段: {self.current_stage}\n"
        report += f"- 总Episodes: {self.total_episodes}\n"
        report += f"- 数据记录数: {len(self.agv_metrics['episodes'])}\n"
        
        # 保存报告
        report_path = os.path.join(self.save_dir, 'performance_report.md')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        return report


if __name__ == "__main__":
    # 测试性能跟踪器
    print("📊 测试性能跟踪器...")
    
    tracker = PerformanceTracker(save_dir="results/test_performance_tracker")
    
    # 模拟性能数据
    np.random.seed(42)
    for episode in range(1, 201):
        stage = min((episode - 1) // 50 + 1, 4)
        
        # 模拟性能指标
        performance_data = {
            'task_completion_rate': min(0.5 + episode * 0.002 + np.random.normal(0, 0.1), 1.0),
            'avg_task_completion_time': max(100 - episode * 0.2 + np.random.normal(0, 10), 20),
            'agv_utilization': min(0.3 + episode * 0.003 + np.random.normal(0, 0.05), 1.0),
            'collision_rate': max(0.1 - episode * 0.0005 + np.random.normal(0, 0.02), 0),
            'path_efficiency': min(0.6 + episode * 0.002 + np.random.normal(0, 0.05), 1.0),
            'load_balance': min(0.4 + episode * 0.003 + np.random.normal(0, 0.05), 1.0),
            'energy_efficiency': min(0.5 + episode * 0.002 + np.random.normal(0, 0.05), 1.0),
            'attention_entropy': 2.0 + np.random.normal(0, 0.3),
            'collaboration_score': min(0.3 + episode * 0.003 + np.random.normal(0, 0.05), 1.0)
        }
        
        tracker.record_performance(episode, stage, performance_data)
    
    # 生成仪表板和报告
    dashboard_path = tracker.generate_performance_dashboard()
    report = tracker.generate_performance_report()
    
    print(f"✅ 性能跟踪器测试完成")
    print(f"  仪表板: {dashboard_path}")
    print(f"  报告生成: 完成")
