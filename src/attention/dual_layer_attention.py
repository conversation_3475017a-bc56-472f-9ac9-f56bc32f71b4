"""
双层注意力机制
整合AGV-任务匹配注意力和AGV协作注意力，实现特征融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import yaml
from typing import Dict, Tuple, Optional, List

from .agv_task_attention import AGVTaskAttention
from .agv_collaboration_attention import AGVCollaborationAttention

class DualLayerAttention(nn.Module):
    """双层注意力网络"""
    
    def __init__(self, config_path: str = "configs/attention_config.yaml"):
        """
        初始化双层注意力网络
        
        Args:
            config_path: 配置文件路径
        """
        super().__init__()
        
        # 加载配置
        self.config = self._load_config(config_path)
        self.fusion_config = self.config['feature_fusion']
        self.attention_config = self.config['attention']
        
        # 初始化两层注意力
        self.layer1_attention = AGVTaskAttention(config_path)
        self.layer2_attention = AGVCollaborationAttention(config_path)
        
        # 特征融合参数
        self.fusion_method = self.fusion_config['strategy']['method']
        self.layer1_weight = self.fusion_config['strategy']['layer1_weight']
        self.layer2_weight = self.fusion_config['strategy']['layer2_weight']
        self.learnable_weights = self.fusion_config['strategy']['learnable_weights']
        
        # 可学习的融合权重
        if self.learnable_weights:
            self.fusion_weights = nn.Parameter(torch.tensor([self.layer1_weight, self.layer2_weight]))
        
        # 输出维度
        output_dim = self.attention_config['dimensions']['output_dim']
        
        # 根据融合方法确定输入维度
        if self.fusion_method == "weighted_concatenation":
            fusion_input_dim = output_dim * 2  # 拼接两层特征
        elif self.fusion_method == "weighted_sum":
            fusion_input_dim = output_dim      # 加权求和
        else:
            raise ValueError(f"不支持的融合方法: {self.fusion_method}")
        
        # 最终MLP处理
        if self.fusion_config['output_processing']['use_final_mlp']:
            mlp_layers = self.fusion_config['output_processing']['final_mlp_layers']
            self.final_mlp = self._build_mlp([fusion_input_dim] + mlp_layers)
            self.final_output_dim = mlp_layers[-1]
        else:
            self.final_mlp = nn.Identity()
            self.final_output_dim = fusion_input_dim
        
        # 批归一化
        if self.fusion_config['output_processing'].get('use_batch_norm', False):
            self.batch_norm = nn.BatchNorm1d(self.final_output_dim)
        else:
            self.batch_norm = nn.Identity()
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _build_mlp(self, layer_sizes: list) -> nn.Module:
        """构建MLP网络"""
        layers = []
        activation = self.fusion_config['output_processing']['activation']
        
        for i in range(len(layer_sizes) - 1):
            layers.append(nn.Linear(layer_sizes[i], layer_sizes[i + 1]))
            if i < len(layer_sizes) - 2:  # 最后一层不加激活函数
                if activation == "relu":
                    layers.append(nn.ReLU())
                elif activation == "gelu":
                    layers.append(nn.GELU())
                elif activation == "tanh":
                    layers.append(nn.Tanh())
                layers.append(nn.Dropout(0.1))
        return nn.Sequential(*layers)
    
    def forward(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                agv_positions: torch.Tensor, task_positions: torch.Tensor,
                agv_loads: torch.Tensor, agv_capacities: torch.Tensor,
                task_weights: torch.Tensor, task_states: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_features: AGV特征 [batch_size, num_agvs, 5]
            task_features: 任务特征 [batch_size, num_tasks, 4]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            task_positions: 任务位置 [batch_size, num_tasks, 2]
            agv_loads: AGV当前载重 [batch_size, num_agvs]
            agv_capacities: AGV载重能力 [batch_size, num_agvs]
            task_weights: 任务重量 [batch_size, num_tasks]
            task_states: 任务状态 [batch_size, num_tasks]
            
        Returns:
            output_dict: 包含融合特征和注意力权重的字典
        """
        # 第一层：AGV-任务匹配注意力
        layer1_output, layer1_attention = self.layer1_attention(
            agv_features, task_features, agv_positions, task_positions,
            agv_loads, agv_capacities, task_weights, task_states
        )
        
        # 第二层：AGV协作注意力
        layer2_output, layer2_attention = self.layer2_attention(
            agv_features, agv_positions, agv_loads, agv_capacities
        )
        
        # 特征融合
        fused_features = self._fuse_features(layer1_output, layer2_output)
        
        # 最终处理
        final_output = self.final_mlp(fused_features)
        
        # 批归一化
        batch_size, num_agvs, feature_dim = final_output.size()
        final_output_reshaped = final_output.view(-1, feature_dim)
        final_output_normalized = self.batch_norm(final_output_reshaped)
        final_output = final_output_normalized.view(batch_size, num_agvs, -1)
        
        return {
            'fused_features': final_output,
            'layer1_features': layer1_output,
            'layer2_features': layer2_output,
            'layer1_attention': layer1_attention,
            'layer2_attention': layer2_attention,
            'fusion_weights': self._get_fusion_weights() if self.learnable_weights else None
        }
    
    def _fuse_features(self, layer1_features: torch.Tensor, layer2_features: torch.Tensor) -> torch.Tensor:
        """
        融合两层特征
        
        Args:
            layer1_features: 第一层特征 [batch_size, num_agvs, output_dim]
            layer2_features: 第二层特征 [batch_size, num_agvs, output_dim]
            
        Returns:
            fused_features: 融合后的特征
        """
        if self.fusion_method == "weighted_concatenation":
            # 加权拼接
            if self.learnable_weights:
                weights = F.softmax(self.fusion_weights, dim=0)
                weighted_layer1 = layer1_features * weights[0]
                weighted_layer2 = layer2_features * weights[1]
            else:
                weighted_layer1 = layer1_features * self.layer1_weight
                weighted_layer2 = layer2_features * self.layer2_weight
            
            fused_features = torch.cat([weighted_layer1, weighted_layer2], dim=-1)
            
        elif self.fusion_method == "weighted_sum":
            # 加权求和
            if self.learnable_weights:
                weights = F.softmax(self.fusion_weights, dim=0)
                fused_features = layer1_features * weights[0] + layer2_features * weights[1]
            else:
                fused_features = (layer1_features * self.layer1_weight +
                                layer2_features * self.layer2_weight)

        elif self.fusion_method == "adaptive_fusion":
            # 自适应融合（基于特征相似度）
            # 计算特征相似度
            similarity = F.cosine_similarity(layer1_features, layer2_features, dim=-1, keepdim=True)

            # 基于相似度调整融合权重
            adaptive_weight1 = torch.sigmoid(similarity)
            adaptive_weight2 = 1.0 - adaptive_weight1

            fused_features = (layer1_features * adaptive_weight1 +
                            layer2_features * adaptive_weight2)

        return fused_features
    
    def _get_fusion_weights(self) -> torch.Tensor:
        """获取当前的融合权重"""
        if self.learnable_weights:
            return F.softmax(self.fusion_weights, dim=0)
        else:
            return torch.tensor([self.layer1_weight, self.layer2_weight])
    
    def get_attention_analysis(self, output_dict: Dict[str, torch.Tensor],
                             agv_positions: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        分析注意力模式
        
        Args:
            output_dict: 前向传播输出
            agv_positions: AGV位置
            
        Returns:
            analysis: 注意力分析结果
        """
        layer1_attention = output_dict['layer1_attention']  # [batch_size, num_heads, num_agvs, num_tasks]
        layer2_attention = output_dict['layer2_attention']  # [batch_size, num_heads, num_agvs, num_agvs]
        
        # 计算注意力熵
        layer1_entropy = self._compute_attention_entropy(layer1_attention)
        layer2_entropy = self._compute_attention_entropy(layer2_attention)
        
        # 计算注意力集中度
        layer1_concentration = self._compute_attention_concentration(layer1_attention)
        layer2_concentration = self._compute_attention_concentration(layer2_attention)
        
        # 获取协作指标
        collaboration_metrics = self.layer2_attention.get_collaboration_metrics(
            layer2_attention, agv_positions
        )
        
        return {
            'layer1_entropy': layer1_entropy,
            'layer2_entropy': layer2_entropy,
            'layer1_concentration': layer1_concentration,
            'layer2_concentration': layer2_concentration,
            'collaboration_metrics': collaboration_metrics,
            'fusion_weights': self._get_fusion_weights()
        }
    
    def _compute_attention_entropy(self, attention_weights: torch.Tensor) -> torch.Tensor:
        """计算注意力熵"""
        # 平均跨头
        avg_attention = attention_weights.mean(dim=1)  # [batch_size, num_agvs, num_targets]
        
        # 计算熵
        entropy = -torch.sum(avg_attention * torch.log(avg_attention + 1e-8), dim=-1)
        return entropy.mean(dim=-1)  # [batch_size]
    
    def _compute_attention_concentration(self, attention_weights: torch.Tensor) -> torch.Tensor:
        """计算注意力集中度（最大注意力权重）"""
        # 平均跨头
        avg_attention = attention_weights.mean(dim=1)  # [batch_size, num_agvs, num_targets]
        
        # 最大注意力权重
        max_attention = torch.max(avg_attention, dim=-1)[0]  # [batch_size, num_agvs]
        return max_attention.mean(dim=-1)  # [batch_size]
