#!/usr/bin/env python3
"""
训练过程监控可视化
实现实时训练曲线绘制和性能指标跟踪
"""

import os
import time
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import seaborn as sns
from collections import deque, defaultdict
from scipy import stats
from scipy.optimize import curve_fit
import warnings
warnings.filterwarnings('ignore')

# 导入字体管理器
from .font_manager import configure_chinese_font

# 配置中文字体
configure_chinese_font()

class TrainingMonitor:
    """训练过程监控器"""
    
    def __init__(self, save_dir: str = "results/training_monitor",
                 update_interval: int = 20, max_history: int = 10000):
        """
        初始化训练监控器

        Args:
            save_dir: 保存目录
            update_interval: 更新间隔（episodes）
            max_history: 最大历史记录数
        """
        self.save_dir = save_dir
        self.update_interval = update_interval
        self.max_history = max_history

        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # 训练历史数据 - 扩展支持更多指标
        self.training_history = {
            'episodes': deque(maxlen=max_history),
            'rewards': deque(maxlen=max_history),
            'episode_lengths': deque(maxlen=max_history),
            'success_rates': deque(maxlen=max_history),
            'stages': deque(maxlen=max_history),
            'timestamps': deque(maxlen=max_history),
            # 新增训练指标
            'entropy': deque(maxlen=max_history),
            'actor_loss': deque(maxlen=max_history),
            'critic_loss': deque(maxlen=max_history),
            'total_loss': deque(maxlen=max_history)
        }
        
        # 阶段统计 - 扩展支持更多指标
        self.stage_stats = defaultdict(lambda: {
            'episodes': [],
            'rewards': [],
            'success_rates': [],
            'entropy': [],
            'actor_loss': [],
            'critic_loss': [],
            'total_loss': [],
            'start_time': None,
            'end_time': None
        })
        
        # 当前状态
        self.current_stage = 1
        self.total_episodes = 0
        self.last_update_episode = 0

        # 图表配置
        self.fig_size = (16, 12)
        self.colors = {
            'reward': '#2E86AB',
            'entropy': '#A23B72',
            'actor_loss': '#F18F01',
            'critic_loss': '#C73E1D',
            'success': '#28A745',
            'length': '#6C757D',
            'stage': '#17A2B8'
        }
        
        print(f"🎯 训练监控器初始化完成")
        print(f"  保存目录: {save_dir}")
        print(f"  更新间隔: {update_interval} episodes")
        print(f"  最大历史: {max_history} records")

    def update_save_dir(self, new_save_dir: str):
        """
        更新保存目录

        Args:
            new_save_dir: 新的保存目录路径
        """
        self.save_dir = new_save_dir
        os.makedirs(new_save_dir, exist_ok=True)
        print(f"📁 训练监控器保存目录已更新: {new_save_dir}")
    
    def record_episode(self, episode: int, reward: float, episode_length: int,
                      success_rate: float, stage: int, entropy: float = 0.0,
                      actor_loss: float = 0.0, critic_loss: float = 0.0,
                      total_loss: float = 0.0) -> None:
        """
        记录单个episode的训练数据

        Args:
            episode: episode编号
            reward: 奖励值
            episode_length: episode长度
            success_rate: 成功率
            stage: 当前阶段
            entropy: 策略熵值
            actor_loss: Actor损失
            critic_loss: Critic损失
            total_loss: 总损失
        """
        # 更新历史数据
        self.training_history['episodes'].append(episode)
        self.training_history['rewards'].append(reward)
        self.training_history['episode_lengths'].append(episode_length)
        self.training_history['success_rates'].append(success_rate)
        self.training_history['stages'].append(stage)
        self.training_history['timestamps'].append(time.time())
        # 新增指标
        self.training_history['entropy'].append(entropy)
        self.training_history['actor_loss'].append(actor_loss)
        self.training_history['critic_loss'].append(critic_loss)
        self.training_history['total_loss'].append(total_loss)
        
        # 更新阶段统计
        if stage != self.current_stage:
            # 结束上一阶段
            if self.current_stage in self.stage_stats:
                self.stage_stats[self.current_stage]['end_time'] = time.time()
            
            # 开始新阶段
            self.current_stage = stage
            self.stage_stats[stage]['start_time'] = time.time()
        
        # 添加到当前阶段统计
        self.stage_stats[stage]['episodes'].append(episode)
        self.stage_stats[stage]['rewards'].append(reward)
        self.stage_stats[stage]['success_rates'].append(success_rate)
        self.stage_stats[stage]['entropy'].append(entropy)
        self.stage_stats[stage]['actor_loss'].append(actor_loss)
        self.stage_stats[stage]['critic_loss'].append(critic_loss)
        self.stage_stats[stage]['total_loss'].append(total_loss)
        
        self.total_episodes = episode
        
        # 检查是否需要更新图表
        if episode - self.last_update_episode >= self.update_interval:
            self.update_plots()
            self.last_update_episode = episode
    
    def update_plots(self) -> None:
        """更新训练图表 - 每个阶段只生成一张固定文件名的图片"""
        if len(self.training_history['episodes']) < 2:
            return

        try:
            # 只创建一张训练指标图，包含当前阶段的所有数据
            self._create_stage_training_metrics()

        except Exception as e:
            print(f"⚠️ 更新训练监控图表时出错: {e}")
            # 确保图表被关闭
            try:
                plt.close('all')
            except:
                pass

    def _create_stage_training_metrics(self) -> None:
        """创建阶段训练指标图（固定文件名，动态更新）"""
        # 获取当前阶段的数据
        current_stage_data = self._get_current_stage_data()

        if not current_stage_data['episodes']:
            return

        # 创建2x2子图布局
        fig, axes = plt.subplots(2, 2, figsize=self.fig_size)
        fig.suptitle(f'阶段{self.current_stage}训练指标 - Episode {self.total_episodes}',
                    fontsize=16, fontweight='bold')

        # 1. 奖励散点图和拟合曲线
        self._plot_metric_scatter_fit(axes[0, 0], current_stage_data, 'rewards',
                                     '奖励', self.colors['reward'])

        # 2. 熵散点图和拟合曲线
        self._plot_metric_scatter_fit(axes[0, 1], current_stage_data, 'entropy',
                                     '策略熵', self.colors['entropy'])

        # 3. Actor损失散点图和拟合曲线
        self._plot_metric_scatter_fit(axes[1, 0], current_stage_data, 'actor_loss',
                                     'Actor损失', self.colors['actor_loss'])

        # 4. Critic损失散点图和拟合曲线
        self._plot_metric_scatter_fit(axes[1, 1], current_stage_data, 'critic_loss',
                                     'Critic损失', self.colors['critic_loss'])

        # 调整布局
        plt.tight_layout()

        # 保存到固定文件名（覆盖之前的文件）
        save_path = os.path.join(self.save_dir, "training_metrics.png")
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig)

        print(f"📊 阶段{self.current_stage}训练指标图已更新: {save_path}")

    def _get_current_stage_data(self) -> Dict[str, List]:
        """获取当前阶段的训练数据"""
        current_stage_data = {
            'episodes': [],
            'rewards': [],
            'entropy': [],
            'actor_loss': [],
            'critic_loss': [],
            'total_loss': []
        }

        # 从历史数据中筛选当前阶段的数据
        episodes = list(self.training_history['episodes'])
        stages = list(self.training_history['stages'])

        for i, stage in enumerate(stages):
            if stage == self.current_stage:
                current_stage_data['episodes'].append(episodes[i])
                current_stage_data['rewards'].append(self.training_history['rewards'][i])
                current_stage_data['entropy'].append(self.training_history['entropy'][i])
                current_stage_data['actor_loss'].append(self.training_history['actor_loss'][i])
                current_stage_data['critic_loss'].append(self.training_history['critic_loss'][i])
                current_stage_data['total_loss'].append(self.training_history['total_loss'][i])

        return current_stage_data

    def _plot_metric_scatter_fit(self, ax, stage_data: Dict, metric_key: str,
                                metric_name: str, color: str):
        """绘制指标的散点图和smooth拟合曲线"""
        episodes = np.array(stage_data['episodes'])
        values = np.array(stage_data[metric_key])

        if len(episodes) < 2:
            ax.text(0.5, 0.5, f'数据不足\n({len(episodes)} episodes)',
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=12, color='gray')
            ax.set_title(f'{metric_name}', fontweight='bold')
            return

        # 散点图
        ax.scatter(episodes, values, alpha=0.7, s=30, color=color,
                  label=f'{metric_name}数据点', zorder=3)

        # Smooth曲线拟合
        if len(episodes) >= 5:
            try:
                # 使用LOWESS平滑
                from scipy.signal import savgol_filter
                if len(values) >= 5:
                    # 确保窗口长度为奇数且不超过数据长度
                    window_length = min(max(5, len(values) // 3), len(values))
                    if window_length % 2 == 0:
                        window_length -= 1

                    smooth_values = savgol_filter(values, window_length, 3)
                    ax.plot(episodes, smooth_values, color=color, linewidth=3,
                           label=f'{metric_name}趋势', alpha=0.8, zorder=4)
            except Exception:
                # 如果LOWESS失败，使用移动平均
                window_size = min(max(3, len(values) // 5), len(values))
                if window_size >= 3:
                    moving_avg = np.convolve(values, np.ones(window_size)/window_size, mode='valid')
                    moving_episodes = episodes[window_size-1:]
                    ax.plot(moving_episodes, moving_avg, color=color, linewidth=3,
                           label=f'{metric_name}趋势', alpha=0.8, zorder=4)

        # 设置图表属性
        ax.set_title(f'{metric_name}', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel(metric_name)
        ax.grid(True, alpha=0.3)
        ax.legend()

        # 添加统计信息
        if len(values) > 0:
            mean_val = np.mean(values)
            std_val = np.std(values)
            latest_val = values[-1]

            stats_text = f'最新: {latest_val:.3f}\n均值: {mean_val:.3f}\n标准差: {std_val:.3f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                   verticalalignment='top', fontsize=9,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

    def _create_basic_plots(self) -> None:
        """创建基础训练图表 - 已禁用，使用_create_stage_training_metrics代替"""
        # 此方法已禁用，避免产生带时间戳的冗余图片
        # 现在使用_create_stage_training_metrics方法生成固定文件名的图片
        pass

    def _create_enhanced_plots(self) -> None:
        """创建增强图表 - 已禁用，使用_create_stage_training_metrics代替"""
        # 此方法已禁用，避免产生带时间戳的冗余图片
        # 现在使用_create_stage_training_metrics方法生成固定文件名的图片
        pass
    
    def _plot_reward_curve(self, ax) -> None:
        """绘制奖励曲线"""
        episodes = list(self.training_history['episodes'])
        rewards = list(self.training_history['rewards'])
        
        # 原始奖励曲线
        ax.plot(episodes, rewards, alpha=0.3, color=self.colors['reward'], linewidth=0.5)
        
        # 移动平均
        if len(rewards) > 10:
            window_size = min(50, len(rewards) // 4)
            moving_avg = np.convolve(rewards, np.ones(window_size)/window_size, mode='valid')
            moving_episodes = episodes[window_size-1:]
            ax.plot(moving_episodes, moving_avg, color=self.colors['reward'], 
                   linewidth=2, label=f'移动平均({window_size})')
        
        ax.set_title('训练奖励曲线', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('奖励')
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    def _plot_success_rate(self, ax) -> None:
        """绘制成功率曲线"""
        episodes = list(self.training_history['episodes'])
        success_rates = list(self.training_history['success_rates'])
        
        ax.plot(episodes, success_rates, color=self.colors['success'], 
               linewidth=2, marker='o', markersize=2)
        
        ax.set_title('成功率变化', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('成功率')
        ax.set_ylim(0, 1)
        ax.grid(True, alpha=0.3)
        
        # 添加阈值线
        ax.axhline(y=0.8, color='red', linestyle='--', alpha=0.7, label='目标阈值(0.8)')
        ax.legend()
    
    def _plot_episode_length(self, ax) -> None:
        """绘制episode长度曲线"""
        episodes = list(self.training_history['episodes'])
        lengths = list(self.training_history['episode_lengths'])
        
        ax.plot(episodes, lengths, color=self.colors['length'], 
               linewidth=1.5, alpha=0.7)
        
        ax.set_title('Episode长度变化', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('步数')
        ax.grid(True, alpha=0.3)
    
    def _plot_stage_progress(self, ax) -> None:
        """绘制阶段进度"""
        episodes = list(self.training_history['episodes'])
        stages = list(self.training_history['stages'])
        
        # 阶段变化点
        stage_changes = []
        for i in range(1, len(stages)):
            if stages[i] != stages[i-1]:
                stage_changes.append((episodes[i], stages[i]))
        
        # 绘制阶段区域
        current_stage = stages[0] if stages else 1
        start_episode = episodes[0] if episodes else 0
        
        for i, (episode, stage) in enumerate(stage_changes):
            ax.axvspan(start_episode, episode, alpha=0.3, 
                      color=plt.cm.Set3(current_stage % 12), 
                      label=f'阶段{current_stage}')
            start_episode = episode
            current_stage = stage
        
        # 最后一个阶段
        if episodes:
            ax.axvspan(start_episode, episodes[-1], alpha=0.3,
                      color=plt.cm.Set3(current_stage % 12),
                      label=f'阶段{current_stage}')
        
        ax.set_title('课程学习阶段进度', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('阶段')
        ax.grid(True, alpha=0.3)
        
        # 限制图例数量
        handles, labels = ax.get_legend_handles_labels()
        if len(handles) > 6:
            ax.legend(handles[-6:], labels[-6:], loc='upper left')
        else:
            ax.legend(loc='upper left')
    
    def generate_training_report(self) -> str:
        """生成训练报告"""
        if not self.training_history['episodes']:
            return "暂无训练数据"
        
        # 计算统计信息
        total_episodes = len(self.training_history['episodes'])
        avg_reward = np.mean(list(self.training_history['rewards']))
        final_success_rate = list(self.training_history['success_rates'])[-1]
        total_stages = len(self.stage_stats)
        
        # 生成报告
        report = f"""
# 训练监控报告

## 训练概况
- **总Episodes**: {total_episodes}
- **平均奖励**: {avg_reward:.3f}
- **最终成功率**: {final_success_rate:.3f}
- **完成阶段**: {total_stages}

## 阶段统计
"""
        
        for stage, stats in sorted(self.stage_stats.items()):
            if stats['episodes']:
                stage_episodes = len(stats['episodes'])
                stage_avg_reward = np.mean(stats['rewards'])
                stage_final_success = stats['success_rates'][-1] if stats['success_rates'] else 0
                
                report += f"""
### 阶段 {stage}
- Episodes: {stage_episodes}
- 平均奖励: {stage_avg_reward:.3f}
- 最终成功率: {stage_final_success:.3f}
"""
        
        # 保存报告
        report_path = os.path.join(self.save_dir, 'training_report.md')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        return report
    
    def save_training_data(self) -> str:
        """保存训练数据"""
        data = {
            'training_history': {
                'episodes': list(self.training_history['episodes']),
                'rewards': list(self.training_history['rewards']),
                'episode_lengths': list(self.training_history['episode_lengths']),
                'success_rates': list(self.training_history['success_rates']),
                'stages': list(self.training_history['stages']),
                'timestamps': list(self.training_history['timestamps'])
            },
            'stage_stats': dict(self.stage_stats),
            'metadata': {
                'total_episodes': self.total_episodes,
                'current_stage': self.current_stage,
                'update_interval': self.update_interval,
                'created_at': datetime.now().isoformat()
            }
        }
        
        data_path = os.path.join(self.save_dir, 'training_data.json')
        os.makedirs(self.save_dir, exist_ok=True)  # 确保目录存在
        with open(data_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return data_path

    def _plot_reward_scatter_fit(self, ax) -> None:
        """绘制奖励散点图和曲线拟合"""
        episodes = np.array(list(self.training_history['episodes']))
        rewards = np.array(list(self.training_history['rewards']))

        # 散点图
        ax.scatter(episodes, rewards, alpha=0.6, s=20, color=self.colors['reward'],
                  label='实际奖励')

        # 多项式拟合
        if len(episodes) >= 3:
            try:
                # 2次多项式拟合
                poly_coeffs = np.polyfit(episodes, rewards, 2)
                poly_fit = np.poly1d(poly_coeffs)

                # 生成拟合曲线
                x_fit = np.linspace(episodes.min(), episodes.max(), 100)
                y_fit = poly_fit(x_fit)

                ax.plot(x_fit, y_fit, color='red', linewidth=2,
                       label='多项式拟合(2次)', linestyle='--')

                # 计算R²
                y_pred = poly_fit(episodes)
                r2 = 1 - np.sum((rewards - y_pred) ** 2) / np.sum((rewards - np.mean(rewards)) ** 2)
                ax.text(0.05, 0.95, f'R² = {r2:.3f}', transform=ax.transAxes,
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

            except Exception as e:
                print(f"多项式拟合失败: {e}")

        # 指数移动平均
        if len(rewards) > 5:
            alpha = 0.1
            ema = [rewards[0]]
            for i in range(1, len(rewards)):
                ema.append(alpha * rewards[i] + (1 - alpha) * ema[-1])

            ax.plot(episodes, ema, color='orange', linewidth=2,
                   label='指数移动平均', alpha=0.8)

        ax.set_title('奖励散点图与拟合分析', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('奖励')
        ax.grid(True, alpha=0.3)
        ax.legend()

    def _plot_success_scatter_fit(self, ax) -> None:
        """绘制成功率散点图和拟合"""
        episodes = np.array(list(self.training_history['episodes']))
        success_rates = np.array(list(self.training_history['success_rates']))

        # 散点图
        ax.scatter(episodes, success_rates, alpha=0.6, s=20, color=self.colors['success'],
                  label='实际成功率')

        # Sigmoid拟合（学习曲线常用）
        if len(episodes) >= 5:
            try:
                def sigmoid(x, a, b, c, d):
                    return a / (1 + np.exp(-b * (x - c))) + d

                # 初始参数估计
                p0 = [1, 0.01, episodes.mean(), 0]

                # 拟合
                popt, _ = curve_fit(sigmoid, episodes, success_rates, p0=p0, maxfev=1000)

                # 生成拟合曲线
                x_fit = np.linspace(episodes.min(), episodes.max(), 100)
                y_fit = sigmoid(x_fit, *popt)

                ax.plot(x_fit, y_fit, color='red', linewidth=2,
                       label='Sigmoid拟合', linestyle='--')

            except Exception as e:
                # 如果Sigmoid拟合失败，使用线性拟合
                try:
                    slope, intercept, r_value, _, _ = stats.linregress(episodes, success_rates)
                    line = slope * episodes + intercept
                    ax.plot(episodes, line, color='red', linewidth=2,
                           label=f'线性拟合 (R={r_value:.3f})', linestyle='--')
                except:
                    pass

        ax.set_title('成功率散点图与拟合分析', fontweight='bold')
        ax.set_xlabel('Episode')
        ax.set_ylabel('成功率')
        ax.set_ylim(0, 1)
        ax.grid(True, alpha=0.3)
        ax.legend()

    def _plot_data_distribution(self, ax) -> None:
        """绘制数据分布分析"""
        rewards = list(self.training_history['rewards'])

        # 直方图
        ax.hist(rewards, bins=30, alpha=0.7, color=self.colors['reward'],
               density=True, label='奖励分布')

        # 正态分布拟合
        if len(rewards) > 10:
            mu, sigma = stats.norm.fit(rewards)
            x = np.linspace(min(rewards), max(rewards), 100)
            y = stats.norm.pdf(x, mu, sigma)
            ax.plot(x, y, 'r-', linewidth=2, label=f'正态拟合 (μ={mu:.2f}, σ={sigma:.2f})')

            # 添加统计信息
            ax.axvline(mu, color='red', linestyle='--', alpha=0.7, label='均值')
            ax.axvline(mu + sigma, color='orange', linestyle=':', alpha=0.7, label='μ+σ')
            ax.axvline(mu - sigma, color='orange', linestyle=':', alpha=0.7, label='μ-σ')

        ax.set_title('奖励分布分析', fontweight='bold')
        ax.set_xlabel('奖励值')
        ax.set_ylabel('密度')
        ax.grid(True, alpha=0.3)
        ax.legend()

    def _plot_correlation_analysis(self, ax) -> None:
        """绘制相关性分析"""
        if len(self.training_history['episodes']) < 10:
            ax.text(0.5, 0.5, '数据不足\n无法进行相关性分析',
                   ha='center', va='center', transform=ax.transAxes)
            ax.set_title('相关性分析', fontweight='bold')
            return

        # 准备数据
        data = {
            '奖励': list(self.training_history['rewards']),
            '成功率': list(self.training_history['success_rates']),
            'Episode长度': list(self.training_history['episode_lengths']),
            '阶段': list(self.training_history['stages'])
        }

        # 计算相关性矩阵
        import pandas as pd
        df = pd.DataFrame(data)

        # 处理零方差列（如成功率全为0的情况）
        # 添加微小的随机噪声避免零方差
        for col in df.columns:
            if df[col].std() == 0:
                # 为零方差列添加微小噪声
                noise = np.random.normal(0, 0.001, len(df))
                df[col] = df[col] + noise

        corr_matrix = df.corr()

        # 处理NaN值
        corr_matrix = corr_matrix.fillna(0)

        # 绘制热力图
        im = ax.imshow(corr_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)

        # 添加标签
        labels = list(corr_matrix.columns)
        ax.set_xticks(range(len(labels)))
        ax.set_yticks(range(len(labels)))
        ax.set_xticklabels(labels, rotation=45)
        ax.set_yticklabels(labels)

        # 添加数值
        for i in range(len(labels)):
            for j in range(len(labels)):
                text = ax.text(j, i, f'{corr_matrix.iloc[i, j]:.2f}',
                             ha="center", va="center", color="black")

        # 添加颜色条
        plt.colorbar(im, ax=ax, shrink=0.8)

        ax.set_title('指标相关性分析', fontweight='bold')


if __name__ == "__main__":
    # 测试训练监控器
    print("🎯 测试训练监控器...")
    
    monitor = TrainingMonitor(save_dir="results/test_training_monitor")
    
    # 模拟训练数据
    np.random.seed(42)
    for episode in range(1, 201):
        # 模拟阶段变化
        stage = min((episode - 1) // 50 + 1, 4)
        
        # 模拟训练指标
        base_reward = stage * 10 + np.random.normal(0, 5)
        reward = max(base_reward + episode * 0.1, -50)
        
        episode_length = max(50 + np.random.normal(0, 10), 10)
        success_rate = min(max(episode * 0.005 + np.random.normal(0, 0.1), 0), 1)
        
        monitor.record_episode(episode, reward, episode_length, success_rate, stage)
    
    # 生成最终报告
    report = monitor.generate_training_report()
    data_path = monitor.save_training_data()
    
    print(f"✅ 训练监控器测试完成")
    print(f"  数据保存: {data_path}")
    print(f"  报告生成: 完成")
