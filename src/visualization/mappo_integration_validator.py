#!/usr/bin/env python3
"""
MAPPO算法集成验证可视化器
提供直观的可视化界面来检查MAPPO算法集成的各个组件状态
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec
import seaborn as sns
from datetime import datetime
from typing import Dict, List, Any, Tuple
import json

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

# 设置中文字体支持
import matplotlib.font_manager as fm
def setup_chinese_fonts():
    chinese_fonts = ['Noto Sans CJK SC', 'WenQuanYi Zen Hei', 'AR PL UMing CN', 'SimHei']
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    for font in chinese_fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font] + plt.rcParams['font.sans-serif']
            break
    plt.rcParams['axes.unicode_minus'] = False

setup_chinese_fonts()


class MAPPOIntegrationValidator:
    """
    MAPPO算法集成验证可视化器
    
    功能：
    - 可视化各个组件的集成状态
    - 生成集成验证仪表板
    - 提供交互式检查界面
    - 生成验证报告
    """
    
    def __init__(self):
        """初始化验证器"""
        self.results_dir = "results/mappo_integration_validation"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 验证结果存储
        self.validation_results = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'components': {},
            'overall_status': 'unknown',
            'success_rate': 0.0
        }
        
        print("🔍 MAPPO算法集成验证可视化器初始化完成")
        print(f"  结果保存目录: {self.results_dir}")
    
    def validate_component(self, component_name: str, check_func, description: str = "") -> bool:
        """
        验证单个组件
        
        Args:
            component_name: 组件名称
            check_func: 检查函数
            description: 组件描述
            
        Returns:
            bool: 验证是否通过
        """
        try:
            result = check_func()
            status = 'success' if result else 'failed'
            
            self.validation_results['components'][component_name] = {
                'status': status,
                'description': description,
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'error': None
            }
            
            print(f"{'✅' if result else '❌'} {component_name}: {description}")
            return result
            
        except Exception as e:
            self.validation_results['components'][component_name] = {
                'status': 'error',
                'description': description,
                'timestamp': datetime.now().strftime('%H:%M:%S'),
                'error': str(e)
            }
            
            print(f"❌ {component_name}: {description} - 错误: {e}")
            return False
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """运行全面的集成验证"""
        print("🚀 开始MAPPO算法集成全面验证...")
        
        # 1. 核心模块验证
        print("\n📦 核心模块验证...")
        
        def check_core_imports():
            from src.mappo import (
                AttentionPolicyNetwork, AttentionValueNetwork,
                MAPPOConfig, CurriculumTrainer,
                MultiAgentAGVEnv, register_attention_models
            )
            return True
        
        self.validate_component(
            'core_imports', 
            check_core_imports,
            '核心模块导入'
        )
        
        def check_config_system():
            from src.mappo import MAPPOConfig
            config = MAPPOConfig('configs/mappo_config.yaml')
            stages = config.get_curriculum_stages()
            return len(stages) == 12
        
        self.validate_component(
            'config_system',
            check_config_system,
            '配置系统 (12个课程阶段)'
        )
        
        def check_environment():
            from src.mappo import MultiAgentAGVEnv
            env_config = {
                'curriculum_stage': 1,
                'env_config_path': 'configs/environment_config.yaml',
                'render_mode': None
            }
            env = MultiAgentAGVEnv(env_config)
            obs, info = env.reset(seed=42)
            env.close()
            return True
        
        self.validate_component(
            'environment',
            check_environment,
            '多智能体环境包装器'
        )
        
        def check_model_registration():
            from src.mappo import register_attention_models
            register_attention_models()
            return True
        
        self.validate_component(
            'model_registration',
            check_model_registration,
            '注意力模型注册'
        )
        
        # 2. 可视化系统验证
        print("\n🎨 可视化系统验证...")
        
        def check_visualization():
            from src.visualization import CurriculumStageVisualizer
            from src.visualization.attention_visualizer import AttentionVisualizer
            visualizer = CurriculumStageVisualizer()
            validation_results = visualizer.validate_stage_configurations()
            return validation_results['summary']['valid_count'] == 12
        
        self.validate_component(
            'visualization',
            check_visualization,
            '可视化系统 (12个阶段验证)'
        )
        
        # 3. 文件完整性验证
        print("\n📁 文件完整性验证...")
        
        def check_files():
            required_files = [
                'src/mappo/__init__.py',
                'src/mappo/attention_policy_network.py',
                'src/mappo/attention_value_network.py',
                'src/mappo/curriculum_trainer.py',
                'configs/mappo_config.yaml',
                'configs/attention_config.yaml'
            ]
            return all(os.path.exists(f) for f in required_files)
        
        self.validate_component(
            'file_integrity',
            check_files,
            '关键文件完整性'
        )
        
        # 4. 训练集成验证
        print("\n🎓 训练集成验证...")
        
        def check_training():
            import ray
            from src.mappo import CurriculumTrainer
            
            if not ray.is_initialized():
                ray.init(local_mode=True, ignore_reinit_error=True)
            
            trainer = CurriculumTrainer(config_path='configs/mappo_config.yaml')
            algo = trainer._create_algorithm(stage=1)
            performance = trainer._evaluate_stage_performance(stage=1, num_episodes=1)
            algo.stop()
            ray.shutdown()
            
            return 'success_rate' in performance
        
        self.validate_component(
            'training_integration',
            check_training,
            '端到端训练流程'
        )
        
        # 计算总体状态
        total_components = len(self.validation_results['components'])
        successful_components = sum(
            1 for comp in self.validation_results['components'].values()
            if comp['status'] == 'success'
        )
        
        self.validation_results['success_rate'] = successful_components / total_components
        
        if self.validation_results['success_rate'] >= 1.0:
            self.validation_results['overall_status'] = 'perfect'
        elif self.validation_results['success_rate'] >= 0.8:
            self.validation_results['overall_status'] = 'good'
        else:
            self.validation_results['overall_status'] = 'needs_improvement'
        
        print(f"\n📊 验证完成: {successful_components}/{total_components} 组件通过")
        print(f"🎯 成功率: {self.validation_results['success_rate']:.1%}")
        
        return self.validation_results
    
    def create_validation_dashboard(self, save_fig: bool = True) -> plt.Figure:
        """
        创建验证仪表板
        
        Args:
            save_fig: 是否保存图片
            
        Returns:
            plt.Figure: 生成的图形对象
        """
        # 创建图形布局
        fig = plt.figure(figsize=(16, 12))
        gs = GridSpec(3, 3, figure=fig, hspace=0.3, wspace=0.3)
        
        # 1. 总体状态指示器
        ax_status = fig.add_subplot(gs[0, :])
        self._draw_overall_status(ax_status)
        
        # 2. 组件状态矩阵
        ax_components = fig.add_subplot(gs[1, :2])
        self._draw_component_matrix(ax_components)
        
        # 3. 成功率饼图
        ax_pie = fig.add_subplot(gs[1, 2])
        self._draw_success_pie(ax_pie)
        
        # 4. 时间线
        ax_timeline = fig.add_subplot(gs[2, :])
        self._draw_validation_timeline(ax_timeline)
        
        # 设置总标题
        fig.suptitle('MAPPO算法集成验证仪表板', fontsize=20, fontweight='bold', y=0.95)
        
        # 保存图片
        if save_fig:
            filename = f"mappo_integration_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            filepath = os.path.join(self.results_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            print(f"📊 验证仪表板已保存: {filepath}")
        
        return fig

    def _draw_overall_status(self, ax):
        """绘制总体状态指示器"""
        ax.clear()

        # 状态颜色映射
        status_colors = {
            'perfect': '#2ECC71',      # 绿色
            'good': '#F39C12',         # 橙色
            'needs_improvement': '#E74C3C'  # 红色
        }

        status_texts = {
            'perfect': '完美收官',
            'good': '基本完成',
            'needs_improvement': '需要改进'
        }

        overall_status = self.validation_results['overall_status']
        success_rate = self.validation_results['success_rate']

        # 绘制状态指示器
        color = status_colors.get(overall_status, '#95A5A6')

        # 大圆形状态指示器
        circle = patches.Circle((0.5, 0.5), 0.3, facecolor=color, edgecolor='black', linewidth=3)
        ax.add_patch(circle)

        # 状态文字
        ax.text(0.5, 0.6, status_texts.get(overall_status, '未知'),
               ha='center', va='center', fontsize=16, fontweight='bold')
        ax.text(0.5, 0.4, f'{success_rate:.1%}',
               ha='center', va='center', fontsize=14)

        # 时间戳
        ax.text(0.5, 0.1, f"验证时间: {self.validation_results['timestamp']}",
               ha='center', va='center', fontsize=10)

        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_aspect('equal')
        ax.axis('off')
        ax.set_title('总体集成状态', fontsize=14, fontweight='bold', pad=20)

    def _draw_component_matrix(self, ax):
        """绘制组件状态矩阵"""
        ax.clear()

        components = self.validation_results['components']
        if not components:
            ax.text(0.5, 0.5, '暂无验证数据', ha='center', va='center')
            ax.set_title('组件状态矩阵', fontsize=14, fontweight='bold')
            return

        # 准备数据
        comp_names = list(components.keys())
        comp_statuses = [components[name]['status'] for name in comp_names]

        # 状态到数值的映射
        status_values = {'success': 1, 'failed': 0, 'error': -1}
        values = [status_values.get(status, 0) for status in comp_statuses]

        # 创建热力图数据
        data = np.array(values).reshape(-1, 1)

        # 绘制热力图
        im = ax.imshow(data, cmap='RdYlGn', aspect='auto', vmin=-1, vmax=1)

        # 设置标签
        ax.set_yticks(range(len(comp_names)))
        ax.set_yticklabels([name.replace('_', ' ').title() for name in comp_names])
        ax.set_xticks([])

        # 添加状态文字
        for i, (name, status) in enumerate(zip(comp_names, comp_statuses)):
            status_text = {'success': '✅', 'failed': '❌', 'error': '⚠️'}.get(status, '?')
            ax.text(0, i, status_text, ha='center', va='center', fontsize=12)

        ax.set_title('组件验证状态', fontsize=14, fontweight='bold')

    def _draw_success_pie(self, ax):
        """绘制成功率饼图"""
        ax.clear()

        components = self.validation_results['components']
        if not components:
            ax.text(0.5, 0.5, '暂无数据', ha='center', va='center')
            return

        # 统计各状态数量
        status_counts = {'success': 0, 'failed': 0, 'error': 0}
        for comp in components.values():
            status = comp['status']
            if status in status_counts:
                status_counts[status] += 1

        # 准备饼图数据
        labels = []
        sizes = []
        colors = []

        status_info = {
            'success': ('成功', '#2ECC71'),
            'failed': ('失败', '#E74C3C'),
            'error': ('错误', '#F39C12')
        }

        for status, count in status_counts.items():
            if count > 0:
                label, color = status_info[status]
                labels.append(f'{label} ({count})')
                sizes.append(count)
                colors.append(color)

        # 绘制饼图
        if sizes:
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors,
                                            autopct='%1.1f%%', startangle=90)

            # 美化文字
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')

        ax.set_title('验证结果分布', fontsize=14, fontweight='bold')

    def _draw_validation_timeline(self, ax):
        """绘制验证时间线"""
        ax.clear()

        components = self.validation_results['components']
        if not components:
            ax.text(0.5, 0.5, '暂无时间线数据', ha='center', va='center')
            return

        # 准备时间线数据
        comp_names = list(components.keys())
        timestamps = [components[name]['timestamp'] for name in comp_names]
        statuses = [components[name]['status'] for name in comp_names]

        # 状态颜色
        status_colors = {'success': '#2ECC71', 'failed': '#E74C3C', 'error': '#F39C12'}
        colors = [status_colors.get(status, '#95A5A6') for status in statuses]

        # 绘制时间线
        y_positions = range(len(comp_names))
        ax.scatter(timestamps, y_positions, c=colors, s=100, alpha=0.7)

        # 添加组件名称
        for i, (name, status) in enumerate(zip(comp_names, statuses)):
            display_name = name.replace('_', ' ').title()
            status_symbol = {'success': '✅', 'failed': '❌', 'error': '⚠️'}.get(status, '?')
            ax.text(timestamps[i], i, f'  {status_symbol} {display_name}',
                   va='center', fontsize=10)

        ax.set_yticks(y_positions)
        ax.set_yticklabels([])
        ax.set_xlabel('验证时间', fontsize=12)
        ax.set_title('验证时间线', fontsize=14, fontweight='bold')

        # 旋转x轴标签
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    def generate_validation_report(self) -> str:
        """生成验证报告"""
        report_path = os.path.join(self.results_dir, 'validation_report.md')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# MAPPO算法集成验证报告\n\n")
            f.write(f"**验证时间**: {self.validation_results['timestamp']}\n\n")

            # 总体状态
            f.write("## 总体状态\n\n")
            status_text = {
                'perfect': '🏆 完美收官',
                'good': '⚠️ 基本完成',
                'needs_improvement': '❌ 需要改进'
            }.get(self.validation_results['overall_status'], '❓ 未知状态')

            f.write(f"**状态**: {status_text}\n")
            f.write(f"**成功率**: {self.validation_results['success_rate']:.1%}\n\n")

            # 组件详情
            f.write("## 组件验证详情\n\n")
            f.write("| 组件 | 状态 | 描述 | 验证时间 |\n")
            f.write("|------|------|------|----------|\n")

            for name, info in self.validation_results['components'].items():
                status_symbol = {'success': '✅', 'failed': '❌', 'error': '⚠️'}.get(info['status'], '?')
                display_name = name.replace('_', ' ').title()
                f.write(f"| {display_name} | {status_symbol} | {info['description']} | {info['timestamp']} |\n")

            # 错误详情
            errors = [(name, info) for name, info in self.validation_results['components'].items()
                     if info['status'] in ['failed', 'error'] and info.get('error')]

            if errors:
                f.write("\n## 错误详情\n\n")
                for name, info in errors:
                    f.write(f"### {name.replace('_', ' ').title()}\n")
                    f.write(f"**错误信息**: {info['error']}\n\n")

            # 建议
            f.write("## 建议\n\n")
            if self.validation_results['success_rate'] >= 1.0:
                f.write("🎉 所有组件验证通过！MAPPO算法集成已完美收官。\n")
            elif self.validation_results['success_rate'] >= 0.8:
                f.write("⚠️ 大部分组件正常，但仍有改进空间。请检查失败的组件。\n")
            else:
                f.write("❌ 多个组件存在问题，需要进行重大修复。\n")

        print(f"📋 验证报告已生成: {report_path}")
        return report_path

    def create_component_detail_view(self, component_name: str, save_fig: bool = True) -> plt.Figure:
        """
        创建单个组件的详细视图

        Args:
            component_name: 组件名称
            save_fig: 是否保存图片

        Returns:
            plt.Figure: 生成的图形对象
        """
        if component_name not in self.validation_results['components']:
            raise ValueError(f"组件 {component_name} 不存在")

        comp_info = self.validation_results['components'][component_name]

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))

        # 1. 状态指示器
        status_colors = {'success': '#2ECC71', 'failed': '#E74C3C', 'error': '#F39C12'}
        color = status_colors.get(comp_info['status'], '#95A5A6')

        circle = patches.Circle((0.5, 0.5), 0.3, facecolor=color, edgecolor='black', linewidth=2)
        ax1.add_patch(circle)

        status_text = {'success': '✅ 成功', 'failed': '❌ 失败', 'error': '⚠️ 错误'}.get(comp_info['status'], '❓ 未知')
        ax1.text(0.5, 0.5, status_text, ha='center', va='center', fontsize=12, fontweight='bold')
        ax1.set_xlim(0, 1)
        ax1.set_ylim(0, 1)
        ax1.set_aspect('equal')
        ax1.axis('off')
        ax1.set_title('验证状态', fontsize=12, fontweight='bold')

        # 2. 组件信息
        ax2.text(0.1, 0.8, f"组件名称: {component_name.replace('_', ' ').title()}", fontsize=10)
        ax2.text(0.1, 0.6, f"描述: {comp_info['description']}", fontsize=10)
        ax2.text(0.1, 0.4, f"验证时间: {comp_info['timestamp']}", fontsize=10)
        ax2.text(0.1, 0.2, f"状态: {comp_info['status']}", fontsize=10)
        ax2.set_xlim(0, 1)
        ax2.set_ylim(0, 1)
        ax2.axis('off')
        ax2.set_title('组件信息', fontsize=12, fontweight='bold')

        # 3. 错误信息（如果有）
        if comp_info.get('error'):
            ax3.text(0.1, 0.5, f"错误信息:\n{comp_info['error']}", fontsize=9,
                    wrap=True, verticalalignment='center')
        else:
            ax3.text(0.5, 0.5, '无错误信息', ha='center', va='center', fontsize=10)
        ax3.set_xlim(0, 1)
        ax3.set_ylim(0, 1)
        ax3.axis('off')
        ax3.set_title('错误详情', fontsize=12, fontweight='bold')

        # 4. 建议
        suggestions = {
            'success': '组件工作正常，无需额外操作。',
            'failed': '组件验证失败，请检查相关配置和依赖。',
            'error': '组件运行时出错，请查看错误信息并进行调试。'
        }
        suggestion = suggestions.get(comp_info['status'], '请联系技术支持。')

        ax4.text(0.1, 0.5, f"建议:\n{suggestion}", fontsize=10,
                wrap=True, verticalalignment='center')
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')
        ax4.set_title('处理建议', fontsize=12, fontweight='bold')

        fig.suptitle(f'{component_name.replace("_", " ").title()} 组件详细视图',
                    fontsize=16, fontweight='bold')

        plt.tight_layout()

        if save_fig:
            filename = f"component_{component_name}_detail.png"
            filepath = os.path.join(self.results_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            print(f"📊 组件详细视图已保存: {filepath}")

        return fig

    def run_interactive_validation(self):
        """运行交互式验证"""
        print("🎮 启动MAPPO算法集成交互式验证...")

        # 运行验证
        results = self.run_comprehensive_validation()

        # 生成仪表板
        dashboard_fig = self.create_validation_dashboard(save_fig=True)

        # 生成报告
        report_path = self.generate_validation_report()

        # 显示结果
        print(f"\n{'='*60}")
        print("🎯 验证结果总结")
        print(f"{'='*60}")
        print(f"总体状态: {results['overall_status']}")
        print(f"成功率: {results['success_rate']:.1%}")
        print(f"验证时间: {results['timestamp']}")

        # 组件状态
        print(f"\n📦 组件状态:")
        for name, info in results['components'].items():
            status_symbol = {'success': '✅', 'failed': '❌', 'error': '⚠️'}.get(info['status'], '?')
            print(f"  {status_symbol} {name.replace('_', ' ').title()}: {info['description']}")

        # 生成组件详细视图
        print(f"\n🔍 生成组件详细视图...")
        for component_name in results['components'].keys():
            try:
                detail_fig = self.create_component_detail_view(component_name, save_fig=True)
                plt.close(detail_fig)
            except Exception as e:
                print(f"⚠️ 生成 {component_name} 详细视图失败: {e}")

        print(f"\n📁 所有结果已保存到: {self.results_dir}")

        # 显示仪表板
        plt.show()

        return results


if __name__ == "__main__":
    # 运行MAPPO算法集成验证可视化
    print("🚀 MAPPO算法集成验证可视化启动...")

    try:
        validator = MAPPOIntegrationValidator()
        results = validator.run_interactive_validation()

        print("\n🎉 MAPPO算法集成验证可视化完成！")

        # 最终状态判断
        if results['success_rate'] >= 1.0:
            print("🏆 恭喜！MAPPO算法集成完美收官！")
        elif results['success_rate'] >= 0.8:
            print("⚠️ MAPPO算法集成基本完成，但仍有改进空间。")
        else:
            print("❌ MAPPO算法集成需要进一步完善。")

    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
