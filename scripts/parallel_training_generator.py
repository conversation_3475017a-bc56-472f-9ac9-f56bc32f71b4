#!/usr/bin/env python3
"""
并行训练脚本生成器
为12个阶段生成独立的训练脚本，支持在不同终端中并行运行
"""

import os
import sys
from datetime import datetime

def generate_stage_training_script(stage: int, base_port: int = 6379) -> str:
    """
    生成单个阶段的训练脚本
    
    Args:
        stage: 训练阶段 (1-12)
        base_port: Ray集群基础端口
        
    Returns:
        str: 训练脚本内容
    """
    
    # 计算该阶段的端口（避免冲突）
    ray_port = base_port + stage
    dashboard_port = 8265 + stage
    
    # 获取阶段配置
    stage_configs = {
        1: (1, 1, 800), 2: (1, 2, 1000), 3: (1, 4, 1200), 4: (2, 4, 1200),
        5: (2, 6, 1300), 6: (2, 8, 1300), 7: (3, 8, 1400), 8: (3, 10, 1400),
        9: (3, 12, 1500), 10: (4, 12, 1600), 11: (4, 14, 1600), 12: (4, 16, 1600)
    }
    
    agv_num, task_num, episodes = stage_configs[stage]
    complexity = agv_num * task_num
    
    script_content = f'''#!/usr/bin/env python3
"""
阶段{stage}独立训练脚本
配置: {agv_num}个AGV, {task_num}个任务, 复杂度{complexity}
目标episodes: {episodes}
"""

import os
import sys
import time
import torch
import ray
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.mappo.curriculum_trainer import CurriculumTrainer


def main():
    """执行阶段{stage}训练"""
    print("🚀 阶段{stage}独立训练启动")
    print("=" * 60)
    
    # 验证GPU环境
    print("🔧 GPU环境检查:")
    print(f"  PyTorch版本: {{torch.__version__}}")
    print(f"  CUDA可用: {{torch.cuda.is_available()}}")
    if torch.cuda.is_available():
        print(f"  GPU设备: {{torch.cuda.get_device_name(0)}}")
        print(f"  GPU数量: {{torch.cuda.device_count()}}")
    print()
    
    # 阶段配置
    stage = {stage}
    agv_num = {agv_num}
    task_num = {task_num}
    target_episodes = {episodes}
    complexity = {complexity}
    
    print(f"📋 阶段{stage}配置:")
    print(f"  AGV数量: {agv_num}")
    print(f"  任务数量: {task_num}")
    print(f"  复杂度: {complexity}")
    print(f"  目标episodes: {episodes}")
    print(f"  Ray端口: {ray_port}")
    print(f"  仪表板端口: {dashboard_port}")
    print()

    # 实验名称
    experiment_name = f"stage_{stage}_parallel_{{datetime.now().strftime('%Y%m%d_%H%M%S')}}"
    config_path = "configs/mappo_config.yaml"
    
    print(f"  实验名称: {{experiment_name}}")
    print(f"  配置文件: {{config_path}}")
    print()
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 初始化Ray（使用独立端口避免冲突）
        if ray.is_initialized():
            ray.shutdown()
        
        print(f"🔧 初始化Ray集群 (端口: {ray_port})...")
        ray.init(
            address=None,  # 本地模式
            dashboard_port={dashboard_port},
            ignore_reinit_error=True,
            log_to_driver=False,
            _temp_dir=f"/tmp/ray_stage_{stage}"
        )
        print("✅ Ray集群初始化完成")
        print()
        
        # 创建课程学习训练器
        print("🎓 初始化课程学习训练器...")
        trainer = CurriculumTrainer(
            config_path=config_path,
            experiment_name=experiment_name,
            enable_visualization=True
        )
        print("✅ 训练器初始化完成")
        print()
        
        # 执行单阶段训练
        print(f"🎯 开始阶段{stage}训练...")
        print(f"  目标: 完成{episodes}个episodes")
        print(f"  预计时间: {{{episodes} * 5 / 3600:.1f}} 小时")
        print()
        
        # 执行训练
        training_result = trainer.train_single_stage(
            stage=stage,
            target_episodes={episodes}
        )
        
        # 计算训练时间
        training_time = time.time() - start_time
        training_hours = training_time / 3600
        
        print()
        print("🎉 阶段{stage}训练完成！")
        print("=" * 60)
        print(f"📊 训练结果:")
        print(f"  训练时长: {{training_hours:.2f}} 小时")
        print(f"  完成episodes: {{training_result.get('total_episodes', 'N/A')}}")
        print(f"  平均奖励: {{training_result.get('avg_reward', 'N/A'):.2f}}")
        print(f"  最终成功率: {{training_result.get('final_success_rate', 'N/A'):.2f}}")
        print(f"  结果目录: results/{{experiment_name}}")
        print()
        
        # 保存训练摘要
        summary = {{
            "stage": stage,
            "agv_num": agv_num,
            "task_num": task_num,
            "complexity": complexity,
            "target_episodes": {episodes},
            "actual_episodes": training_result.get('total_episodes', 0),
            "training_time_hours": training_hours,
            "avg_reward": training_result.get('avg_reward', 0),
            "final_success_rate": training_result.get('final_success_rate', 0),
            "experiment_name": experiment_name,
            "completion_time": datetime.now().isoformat()
        }}
        
        summary_file = f"results/{{experiment_name}}/stage_{stage}_summary.json"
        os.makedirs(os.path.dirname(summary_file), exist_ok=True)
        
        import json
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"📄 训练摘要已保存: {{summary_file}}")
        
    except Exception as e:
        print(f"❌ 阶段{stage}训练失败: {{e}}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        try:
            if 'trainer' in locals():
                print("🧹 清理训练器资源...")
                trainer.cleanup()
            
            print("🔧 关闭Ray集群...")
            ray.shutdown()
            print("✅ 资源清理完成")
            
        except Exception as e:
            print(f"⚠️ 资源清理时出错: {{e}}")
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
'''
    
    return script_content


def generate_all_training_scripts():
    """生成所有12个阶段的训练脚本"""
    print("🏭 生成12个阶段的并行训练脚本")
    print("=" * 60)
    
    # 创建scripts/parallel目录
    parallel_dir = "scripts/parallel"
    os.makedirs(parallel_dir, exist_ok=True)
    
    generated_scripts = []
    
    for stage in range(1, 13):
        script_content = generate_stage_training_script(stage)
        script_filename = f"train_stage_{stage:02d}.py"
        script_path = os.path.join(parallel_dir, script_filename)
        
        # 写入脚本文件
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 设置执行权限
        os.chmod(script_path, 0o755)
        
        generated_scripts.append(script_path)
        print(f"✅ 生成阶段{stage:2d}脚本: {script_path}")
    
    return generated_scripts


def generate_launcher_script(scripts: list):
    """生成启动器脚本"""
    launcher_content = f'''#!/bin/bash
"""
12阶段并行训练启动器
在不同终端中启动所有阶段的训练
"""

echo "🚀 启动12阶段并行训练"
echo "=============================="

# 检查conda环境
if [[ "$CONDA_DEFAULT_ENV" != "biye_RL" ]]; then
    echo "❌ 请先激活biye_RL环境: conda activate biye_RL"
    exit 1
fi

# 检查GPU可用性
python -c "import torch; print(f'GPU可用: {{torch.cuda.is_available()}}')"

echo ""
echo "📋 将启动以下训练脚本:"
'''
    
    for i, script in enumerate(scripts, 1):
        script_name = os.path.basename(script)
        launcher_content += f'echo "  {i:2d}. {script_name}"\n'
    
    launcher_content += '''
echo ""
read -p "确认启动所有训练? (y/N): " confirm

if [[ $confirm != [yY] ]]; then
    echo "❌ 取消启动"
    exit 0
fi

echo ""
echo "🚀 开始启动训练进程..."

# 启动所有训练脚本（在后台运行）
'''
    
    for i, script in enumerate(scripts, 1):
        script_name = os.path.basename(script)
        launcher_content += f'''
echo "启动阶段{i:2d}训练..."
gnome-terminal --title="阶段{i:2d}训练" -- bash -c "cd $(pwd) && python {script} 2>&1 | tee logs/stage_{i:02d}_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突
'''
    
    launcher_content += '''
echo ""
echo "✅ 所有训练进程已启动"
echo "💡 提示:"
echo "  - 每个阶段在独立终端中运行"
echo "  - 日志保存在logs/目录中"
echo "  - 结果保存在results/目录中"
echo "  - 使用 'ps aux | grep python' 查看运行状态"
echo ""
'''
    
    launcher_path = "scripts/parallel/launch_all_stages.sh"
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    os.chmod(launcher_path, 0o755)
    return launcher_path


def main():
    """主函数"""
    print("🏭 12阶段并行训练脚本生成器")
    print("=" * 60)
    
    # 创建必要目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("results", exist_ok=True)
    
    # 生成所有训练脚本
    scripts = generate_all_training_scripts()
    
    # 生成启动器脚本
    launcher = generate_launcher_script(scripts)
    
    print()
    print("🎉 脚本生成完成！")
    print("=" * 60)
    print(f"📁 生成了 {len(scripts)} 个训练脚本")
    print(f"📄 启动器脚本: {launcher}")
    print()
    print("💡 使用方法:")
    print("1. 确保在biye_RL环境中: conda activate biye_RL")
    print("2. 运行启动器: bash scripts/parallel/launch_all_stages.sh")
    print("3. 或手动运行单个阶段: python scripts/parallel/train_stage_XX.py")
    print()
    print("⚠️ 注意事项:")
    print("- 每个阶段使用独立的Ray集群和端口")
    print("- 建议监控GPU内存使用情况")
    print("- 日志文件保存在logs/目录中")
    print("- 可以随时停止单个阶段的训练")


if __name__ == "__main__":
    main()
