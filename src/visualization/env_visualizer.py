"""
AGV环境可视化工具
生成环境地图和AGV运行状态的可视化图像
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import os
from typing import List, Tuple, Optional
import yaml

class EnvironmentVisualizer:
    """环境可视化器"""
    
    def __init__(self, config_path: str = "configs/environment_config.yaml"):
        """
        初始化可视化器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.colors = self.config['visualization']['colors']
        
        # 创建颜色映射
        self.colormap = ListedColormap([
            np.array(self.colors['empty']) / 255.0,    # 空地 - 白色
            np.array(self.colors['wall']) / 255.0,     # 墙壁 - 黑色  
            np.array(self.colors['shelf']) / 255.0,    # 货架 - 棕色
            np.array(self.colors['start']) / 255.0,    # 起点 - 绿色
            np.array(self.colors['end']) / 255.0,      # 终点 - 橙色
        ])
    
    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def visualize_environment(self, env, save_path: Optional[str] = None, 
                            show_agv_ids: bool = True, show_task_info: bool = True) -> None:
        """
        可视化环境状态
        
        Args:
            env: AGV环境对象
            save_path: 保存路径
            show_agv_ids: 是否显示AGV编号
            show_task_info: 是否显示任务信息
        """
        fig, ax = plt.subplots(1, 1, figsize=(12, 6))
        
        # 绘制地图
        ax.imshow(env.map_grid, cmap=self.colormap, vmin=0, vmax=4)
        
        # 绘制AGV
        for agv in env.agvs:
            # AGV位置用蓝色圆圈表示
            circle = patches.Circle((agv.x, agv.y), 0.3, 
                                  color=np.array(self.colors['agv']) / 255.0, 
                                  alpha=0.8, zorder=3)
            ax.add_patch(circle)
            
            if show_agv_ids:
                # 显示AGV编号
                ax.text(agv.x, agv.y, str(agv.id), 
                       ha='center', va='center', fontsize=10, 
                       fontweight='bold', color='white', zorder=4)
        
        # 绘制任务（在货架上）
        for task in env.tasks:
            if task.state == 0:  # 未分配任务
                # 用红色圆圈表示任务（在货架上）
                circle = patches.Circle((task.x, task.y), 0.25,
                                      color=np.array(self.colors['task']) / 255.0,
                                      alpha=0.9, zorder=4)
                ax.add_patch(circle)

                if show_task_info:
                    # 显示任务重量（在任务圆圈内）
                    ax.text(task.x, task.y, str(task.weight),
                           ha='center', va='center', fontsize=8,
                           color='white', fontweight='bold', zorder=5)
        
        # 设置图形属性
        ax.set_xlim(-0.5, env.map_grid.shape[1] - 0.5)
        ax.set_ylim(-0.5, env.map_grid.shape[0] - 0.5)
        ax.set_aspect('equal')
        ax.invert_yaxis()  # 翻转Y轴使(0,0)在左上角
        
        # 添加网格
        ax.set_xticks(range(env.map_grid.shape[1]))
        ax.set_yticks(range(env.map_grid.shape[0]))
        ax.grid(True, alpha=0.3)
        
        # 添加标题和信息
        completed_tasks = sum(1 for task in env.tasks if task.state == 2)
        title = f"AGV仓储环境 - 阶段{env.curriculum_stage}\n"
        title += f"AGV: {env.num_agvs}, 任务: {completed_tasks}/{env.num_tasks}, "
        title += f"步数: {env.current_step}/{env.max_steps}"
        ax.set_title(title, fontsize=14, fontweight='bold')
        
        # 添加图例
        legend_elements = [
            patches.Patch(color=np.array(self.colors['empty']) / 255.0, label='通道'),
            patches.Patch(color=np.array(self.colors['shelf']) / 255.0, label='货架'),
            patches.Patch(color=np.array(self.colors['agv']) / 255.0, label='AGV'),
            patches.Patch(color=np.array(self.colors['task']) / 255.0, label='未完成任务'),
            patches.Patch(color=np.array(self.colors['start']) / 255.0, label='起点'),
            patches.Patch(color=np.array(self.colors['end']) / 255.0, label='终点')
        ]
        ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1.02, 1))
        
        plt.tight_layout()
        
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"环境可视化已保存到: {save_path}")
        
        plt.show()
    
    def create_stage_visualization(self, stage: int, save_dir: str = "results/environment_imgs"):
        """
        创建特定阶段的环境可视化
        
        Args:
            stage: 课程学习阶段
            save_dir: 保存目录
        """
        # 导入环境类
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
        from environment.agv_warehouse_env import AGVWarehouseEnv
        
        # 创建环境
        env = AGVWarehouseEnv(curriculum_stage=stage)
        obs, info = env.reset(seed=42)
        
        # 保存路径
        save_path = os.path.join(save_dir, f"stage_{stage}_environment.png")
        
        # 可视化
        self.visualize_environment(env, save_path=save_path)
        
        return save_path
    
    def create_all_stages_visualization(self, save_dir: str = "results/environment_imgs"):
        """
        创建所有阶段的环境可视化
        
        Args:
            save_dir: 保存目录
        """
        print("🎨 创建所有课程学习阶段的环境可视化...")
        
        os.makedirs(save_dir, exist_ok=True)
        
        for stage in range(1, 13):
            try:
                save_path = self.create_stage_visualization(stage, save_dir)
                print(f"✅ 阶段 {stage} 可视化完成: {save_path}")
            except Exception as e:
                print(f"❌ 阶段 {stage} 可视化失败: {e}")
        
        print(f"🎉 所有阶段可视化完成！保存在: {save_dir}")

if __name__ == "__main__":
    # 测试可视化工具
    print("🎨 测试环境可视化工具...")
    
    # 创建可视化器
    visualizer = EnvironmentVisualizer()
    
    # 测试单个阶段可视化
    try:
        save_path = visualizer.create_stage_visualization(stage=1)
        print(f"✅ 单阶段可视化测试成功: {save_path}")
    except Exception as e:
        print(f"❌ 单阶段可视化测试失败: {e}")
    
    # 询问是否创建所有阶段的可视化
    response = input("\n是否创建所有12个阶段的环境可视化？(y/n): ")
    if response.lower() == 'y':
        visualizer.create_all_stages_visualization()
    
    print("✅ 可视化工具测试完成！")
