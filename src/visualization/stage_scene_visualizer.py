#!/usr/bin/env python3
"""
阶段环境场景可视化器
专门为每个训练阶段生成环境场景图，显示AGV起点、终点、货架、任务和当前AGV位置
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
from typing import Optional, Dict, Any
import yaml

# 导入字体管理器
from .font_manager import configure_chinese_font

# 配置中文字体
configure_chinese_font()

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.environment.agv_warehouse_env import AGVWarehouseEnv


class StageSceneVisualizer:
    """阶段环境场景可视化器"""
    
    def __init__(self, config_path: str = "configs/environment_config.yaml"):
        """
        初始化可视化器
        
        Args:
            config_path: 环境配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config(config_path)
        
        # 设置默认颜色（如果配置文件不存在）
        self.colors = self._get_colors()
        
        # 创建颜色映射
        self.colormap = ListedColormap([
            np.array(self.colors['empty']) / 255.0,    # 空地 - 白色
            np.array(self.colors['wall']) / 255.0,     # 墙壁 - 黑色  
            np.array(self.colors['shelf']) / 255.0,    # 货架 - 棕色
            np.array(self.colors['start']) / 255.0,    # 起点 - 绿色
            np.array(self.colors['end']) / 255.0,      # 终点 - 橙色
        ])
        
        print(f"🎨 阶段场景可视化器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"⚠️  配置文件未找到: {config_path}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'visualization': {
                'colors': {
                    'empty': [255, 255, 255],    # 白色
                    'wall': [0, 0, 0],           # 黑色
                    'shelf': [139, 69, 19],      # 棕色
                    'agv': [0, 100, 200],        # 蓝色
                    'task': [255, 0, 0],         # 红色
                    'start': [0, 255, 0],        # 绿色
                    'end': [255, 165, 0]         # 橙色
                }
            }
        }
    
    def _get_colors(self) -> Dict:
        """获取颜色配置"""
        if 'visualization' in self.config and 'colors' in self.config['visualization']:
            return self.config['visualization']['colors']
        else:
            return self._get_default_config()['visualization']['colors']
    
    def generate_stage_scene(self, stage: int, save_dir: str, 
                           seed: int = 42, show_details: bool = True) -> str:
        """
        生成指定阶段的环境场景图
        
        Args:
            stage: 课程学习阶段 (1-12)
            save_dir: 保存目录
            seed: 随机种子，确保可重现性
            show_details: 是否显示详细信息
            
        Returns:
            str: 保存的图片路径
        """
        print(f"🎯 生成阶段{stage}环境场景图...")
        
        try:
            # 创建环境
            env = AGVWarehouseEnv(
                config_path=self.config_path,
                curriculum_stage=stage
            )
            
            # 重置环境到初始状态
            obs, info = env.reset(seed=seed)
            
            # 生成可视化图
            save_path = self._create_visualization(env, save_dir, show_details)
            
            print(f"✅ 阶段{stage}环境场景图已生成: {save_path}")
            return save_path
            
        except Exception as e:
            print(f"❌ 生成阶段{stage}环境场景图失败: {e}")
            # 创建一个错误占位图
            return self._create_error_placeholder(stage, save_dir, str(e))
    
    def _create_visualization(self, env, save_dir: str, show_details: bool) -> str:
        """
        创建环境可视化图
        
        Args:
            env: AGV环境对象
            save_dir: 保存目录
            show_details: 是否显示详细信息
            
        Returns:
            str: 保存的图片路径
        """
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(14, 8))
        
        # 绘制地图背景
        ax.imshow(env.map_grid, cmap=self.colormap, vmin=0, vmax=4, alpha=0.8)
        
        # 绘制AGV
        self._draw_agvs(ax, env.agvs, show_details)
        
        # 绘制任务
        self._draw_tasks(ax, env.tasks, show_details)
        
        # 设置图形属性
        self._setup_plot_properties(ax, env)
        
        # 添加标题和信息
        self._add_title_and_info(ax, env)
        
        # 添加图例
        self._add_legend(ax)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        save_path = os.path.join(save_dir, "environment_scene.png")
        os.makedirs(save_dir, exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig)  # 释放内存
        
        return save_path
    
    def _draw_agvs(self, ax, agvs, show_details: bool):
        """绘制AGV"""
        for agv in agvs:
            # AGV位置用蓝色圆圈表示
            circle = patches.Circle(
                (agv.x, agv.y), 0.35, 
                color=np.array(self.colors['agv']) / 255.0, 
                alpha=0.9, zorder=5, linewidth=2, edgecolor='white'
            )
            ax.add_patch(circle)
            
            if show_details:
                # 显示AGV编号
                ax.text(agv.x, agv.y, str(agv.id), 
                       ha='center', va='center', fontsize=12, 
                       fontweight='bold', color='white', zorder=6)
                
                # 显示载重信息
                load_text = f"{agv.current_load}/{agv.max_capacity}"
                ax.text(agv.x, agv.y - 0.6, load_text,
                       ha='center', va='center', fontsize=8,
                       bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8),
                       zorder=6)
    
    def _draw_tasks(self, ax, tasks, show_details: bool):
        """绘制任务"""
        for task in tasks:
            # 根据任务状态选择颜色
            if task.state == 0:  # 未分配
                color = np.array(self.colors['task']) / 255.0
                alpha = 0.8
            elif task.state == 1:  # 已分配
                color = np.array([255, 255, 0]) / 255.0  # 黄色
                alpha = 0.7
            else:  # 已完成
                color = np.array([128, 128, 128]) / 255.0  # 灰色
                alpha = 0.5
            
            # 任务位置用方形表示
            square = patches.Rectangle(
                (task.x - 0.25, task.y - 0.25), 0.5, 0.5,
                color=color, alpha=alpha, zorder=4,
                linewidth=1, edgecolor='black'
            )
            ax.add_patch(square)
            
            if show_details:
                # 显示任务权重
                ax.text(task.x, task.y, str(task.weight),
                       ha='center', va='center', fontsize=8,
                       fontweight='bold', color='black', zorder=5)
    
    def _setup_plot_properties(self, ax, env):
        """设置图形属性"""
        ax.set_xlim(-0.5, env.map_grid.shape[1] - 0.5)
        ax.set_ylim(-0.5, env.map_grid.shape[0] - 0.5)
        ax.set_aspect('equal')
        ax.invert_yaxis()  # 翻转Y轴使(0,0)在左上角
        
        # 添加网格
        ax.set_xticks(range(env.map_grid.shape[1]))
        ax.set_yticks(range(env.map_grid.shape[0]))
        ax.grid(True, alpha=0.3, linewidth=0.5)
        
        # 设置坐标轴标签
        ax.set_xlabel('X坐标', fontsize=12)
        ax.set_ylabel('Y坐标', fontsize=12)
    
    def _add_title_and_info(self, ax, env):
        """添加标题和信息"""
        completed_tasks = sum(1 for task in env.tasks if task.state == 2)
        pending_tasks = sum(1 for task in env.tasks if task.state == 0)
        assigned_tasks = sum(1 for task in env.tasks if task.state == 1)
        
        title = f"AGV仓储环境场景 - 阶段{env.curriculum_stage}"
        subtitle = f"AGV数量: {env.num_agvs} | 任务总数: {env.num_tasks} | 最大步数: {env.max_steps}"
        info = f"未分配: {pending_tasks} | 已分配: {assigned_tasks} | 已完成: {completed_tasks}"
        
        ax.set_title(f"{title}\n{subtitle}\n{info}", 
                    fontsize=14, fontweight='bold', pad=20)
    
    def _add_legend(self, ax):
        """添加图例"""
        legend_elements = [
            patches.Patch(color=np.array(self.colors['empty']) / 255.0, label='通道'),
            patches.Patch(color=np.array(self.colors['shelf']) / 255.0, label='货架'),
            patches.Patch(color=np.array(self.colors['agv']) / 255.0, label='AGV'),
            patches.Patch(color=np.array(self.colors['task']) / 255.0, label='未分配任务'),
            patches.Patch(color=np.array([255, 255, 0]) / 255.0, label='已分配任务'),
            patches.Patch(color=np.array([128, 128, 128]) / 255.0, label='已完成任务'),
            patches.Patch(color=np.array(self.colors['start']) / 255.0, label='起点'),
            patches.Patch(color=np.array(self.colors['end']) / 255.0, label='终点')
        ]
        ax.legend(handles=legend_elements, loc='center left', bbox_to_anchor=(1.02, 0.5))
    
    def _create_error_placeholder(self, stage: int, save_dir: str, error_msg: str) -> str:
        """创建错误占位图"""
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))
        
        ax.text(0.5, 0.5, f"阶段{stage}环境场景图生成失败\n\n错误信息:\n{error_msg}",
                ha='center', va='center', fontsize=14, color='red',
                bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8))
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_title(f"阶段{stage}环境场景图 - 错误", fontsize=16, fontweight='bold')
        ax.axis('off')
        
        save_path = os.path.join(save_dir, "environment_scene.png")
        os.makedirs(save_dir, exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig)
        
        return save_path


if __name__ == "__main__":
    # 测试阶段场景可视化器
    print("🎨 测试阶段场景可视化器...")
    
    visualizer = StageSceneVisualizer()
    
    # 测试生成阶段1的场景图
    save_path = visualizer.generate_stage_scene(
        stage=1, 
        save_dir="results/test_stage_scene",
        show_details=True
    )
    
    print(f"✅ 测试完成，场景图保存到: {save_path}")
