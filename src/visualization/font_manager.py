#!/usr/bin/env python3
"""
字体管理模块
检测系统可用的中文字体并配置matplotlib
"""

import os
import platform
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from typing import List, Optional


class FontManager:
    """字体管理器"""
    
    def __init__(self):
        self.system = platform.system()
        self.available_fonts = self._get_available_fonts()
        self.chinese_font = self._find_best_chinese_font()
        
    def _get_available_fonts(self) -> List[str]:
        """获取系统可用字体列表"""
        font_list = [f.name for f in fm.fontManager.ttflist]
        return list(set(font_list))  # 去重
    
    def _find_best_chinese_font(self) -> Optional[str]:
        """查找最佳的中文字体"""
        # 按优先级排序的中文字体列表
        chinese_fonts = [
            # Linux常见中文字体
            'Noto Sans CJK SC',
            'Noto Sans CJK TC', 
            'Source Han Sans SC',
            'Source Han Sans TC',
            'WenQuanYi Micro Hei',
            'WenQuanYi Zen Hei',
            'AR PL UMing CN',
            'AR PL UKai CN',
            'Droid Sans Fallback',
            
            # Windows中文字体
            'SimHei',
            'Microsoft YaHei',
            'SimSun',
            'KaiTi',
            'FangSong',
            
            # macOS中文字体
            'PingFang SC',
            'Hiragino Sans GB',
            'STHeiti',
            'Arial Unicode MS',
            
            # 通用备选
            'DejaVu Sans',
            'Liberation Sans',
            'sans-serif'
        ]
        
        # 查找第一个可用的中文字体
        for font in chinese_fonts:
            if font in self.available_fonts:
                print(f"✅ 找到中文字体: {font}")
                return font
        
        # 如果没有找到专门的中文字体，尝试检测支持中文的字体
        for font_name in self.available_fonts:
            if self._test_chinese_support(font_name):
                print(f"✅ 检测到支持中文的字体: {font_name}")
                return font_name
        
        print("⚠️  未找到支持中文的字体，将使用默认字体")
        return 'DejaVu Sans'
    
    def _test_chinese_support(self, font_name: str) -> bool:
        """测试字体是否支持中文"""
        try:
            # 尝试查找字体文件
            font_files = [f for f in fm.fontManager.ttflist if f.name == font_name]
            if not font_files:
                return False
            
            # 简单的中文字符测试
            test_chars = ['中', '文', '字', '体']
            font_file = font_files[0]
            
            # 这里简化处理，主要基于字体名称判断
            font_name_lower = font_name.lower()
            chinese_indicators = [
                'cjk', 'han', 'chinese', 'zh', 'cn', 'sc', 'tc',
                'noto', 'source', 'wenquanyi', 'simhei', 'simsun',
                'microsoft', 'yahei', 'pingfang', 'hiragino'
            ]
            
            return any(indicator in font_name_lower for indicator in chinese_indicators)
            
        except Exception:
            return False
    
    def configure_matplotlib(self):
        """配置matplotlib使用中文字体"""
        if self.chinese_font:
            # 设置字体
            plt.rcParams['font.sans-serif'] = [self.chinese_font, 'DejaVu Sans', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            
            print(f"📝 matplotlib中文字体配置完成: {self.chinese_font}")
        else:
            print("❌ matplotlib中文字体配置失败")
    
    def get_font_info(self) -> dict:
        """获取字体信息"""
        return {
            'system': self.system,
            'chinese_font': self.chinese_font,
            'total_fonts': len(self.available_fonts),
            'font_config': plt.rcParams['font.sans-serif']
        }
    
    def list_chinese_fonts(self) -> List[str]:
        """列出所有可能的中文字体"""
        chinese_fonts = []
        chinese_keywords = [
            'cjk', 'han', 'chinese', 'zh', 'cn', 'sc', 'tc',
            'noto', 'source', 'wenquanyi', 'simhei', 'simsun',
            'microsoft', 'yahei', 'pingfang', 'hiragino', 'kaiti',
            'fangsong', 'arial unicode'
        ]
        
        for font in self.available_fonts:
            font_lower = font.lower()
            if any(keyword in font_lower for keyword in chinese_keywords):
                chinese_fonts.append(font)
        
        return sorted(chinese_fonts)


# 全局字体管理器实例
_font_manager = None

def get_font_manager() -> FontManager:
    """获取全局字体管理器实例"""
    global _font_manager
    if _font_manager is None:
        _font_manager = FontManager()
        _font_manager.configure_matplotlib()
    return _font_manager

def configure_chinese_font():
    """配置中文字体（便捷函数）"""
    font_manager = get_font_manager()
    return font_manager.chinese_font

def get_chinese_font() -> str:
    """获取当前配置的中文字体"""
    font_manager = get_font_manager()
    return font_manager.chinese_font or 'DejaVu Sans'


if __name__ == "__main__":
    # 测试字体管理器
    print("🔤 测试字体管理器")
    print("=" * 50)
    
    fm = FontManager()
    
    print(f"系统: {fm.system}")
    print(f"总字体数: {len(fm.available_fonts)}")
    print(f"选择的中文字体: {fm.chinese_font}")
    
    print(f"\n🔍 可用的中文字体:")
    chinese_fonts = fm.list_chinese_fonts()
    for i, font in enumerate(chinese_fonts[:10], 1):  # 只显示前10个
        print(f"  {i}. {font}")
    
    if len(chinese_fonts) > 10:
        print(f"  ... 还有 {len(chinese_fonts) - 10} 个字体")
    
    # 配置matplotlib
    fm.configure_matplotlib()
    
    print(f"\n📝 matplotlib配置:")
    print(f"  font.sans-serif: {plt.rcParams['font.sans-serif']}")
    print(f"  axes.unicode_minus: {plt.rcParams['axes.unicode_minus']}")
    
    print("\n✅ 字体管理器测试完成")
