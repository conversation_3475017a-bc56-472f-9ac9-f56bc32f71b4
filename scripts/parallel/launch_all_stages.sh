#!/bin/bash
"""
12阶段并行训练启动器
在不同终端中启动所有阶段的训练
"""

echo "🚀 启动12阶段并行训练"
echo "=============================="

# 检查conda环境
if [[ "$CONDA_DEFAULT_ENV" != "biye_RL" ]]; then
    echo "❌ 请先激活biye_RL环境: conda activate biye_RL"
    exit 1
fi

# 检查GPU可用性
python -c "import torch; print(f'GPU可用: {torch.cuda.is_available()}')"

echo ""
echo "📋 将启动以下训练脚本:"
echo "   1. train_stage_01.py"
echo "   2. train_stage_02.py"
echo "   3. train_stage_03.py"
echo "   4. train_stage_04.py"
echo "   5. train_stage_05.py"
echo "   6. train_stage_06.py"
echo "   7. train_stage_07.py"
echo "   8. train_stage_08.py"
echo "   9. train_stage_09.py"
echo "  10. train_stage_10.py"
echo "  11. train_stage_11.py"
echo "  12. train_stage_12.py"

echo ""
read -p "确认启动所有训练? (y/N): " confirm

if [[ $confirm != [yY] ]]; then
    echo "❌ 取消启动"
    exit 0
fi

echo ""
echo "🚀 开始启动训练进程..."

# 启动所有训练脚本（在后台运行）

echo "启动阶段 1训练..."
gnome-terminal --title="阶段 1训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_01.py 2>&1 | tee logs/stage_01_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo "启动阶段 2训练..."
gnome-terminal --title="阶段 2训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_02.py 2>&1 | tee logs/stage_02_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo "启动阶段 3训练..."
gnome-terminal --title="阶段 3训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_03.py 2>&1 | tee logs/stage_03_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo "启动阶段 4训练..."
gnome-terminal --title="阶段 4训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_04.py 2>&1 | tee logs/stage_04_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo "启动阶段 5训练..."
gnome-terminal --title="阶段 5训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_05.py 2>&1 | tee logs/stage_05_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo "启动阶段 6训练..."
gnome-terminal --title="阶段 6训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_06.py 2>&1 | tee logs/stage_06_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo "启动阶段 7训练..."
gnome-terminal --title="阶段 7训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_07.py 2>&1 | tee logs/stage_07_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo "启动阶段 8训练..."
gnome-terminal --title="阶段 8训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_08.py 2>&1 | tee logs/stage_08_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo "启动阶段 9训练..."
gnome-terminal --title="阶段 9训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_09.py 2>&1 | tee logs/stage_09_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo "启动阶段10训练..."
gnome-terminal --title="阶段10训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_10.py 2>&1 | tee logs/stage_10_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo "启动阶段11训练..."
gnome-terminal --title="阶段11训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_11.py 2>&1 | tee logs/stage_11_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo "启动阶段12训练..."
gnome-terminal --title="阶段12训练" -- bash -c "cd $(pwd) && python scripts/parallel/train_stage_12.py 2>&1 | tee logs/stage_12_$(date +%Y%m%d_%H%M%S).log; read -p '训练完成，按Enter关闭...'"
sleep 2  # 避免同时启动造成冲突

echo ""
echo "✅ 所有训练进程已启动"
echo "💡 提示:"
echo "  - 每个阶段在独立终端中运行"
echo "  - 日志保存在logs/目录中"
echo "  - 结果保存在results/目录中"
echo "  - 使用 'ps aux | grep python' 查看运行状态"
echo ""
