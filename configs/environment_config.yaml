# AGV仓储环境配置文件

# 环境基础设置
environment:
  name: "AGVWarehouse"
  version: "1.0.0"
  
# 地图配置
map:
  width: 26          # 地图宽度
  height: 10         # 地图高度
  
  # 货架配置
  shelves:
    count: 15         # 货架数量
    rows: 3           # 货架行数
    cols: 5           # 货架列数
    shelf_width: 4    # 单个货架宽度
    shelf_height: 2   # 单个货架高度
    
  # 通道配置
  corridor_width: 1   # 通道宽度
  
  # 边界点
  start_point: [0, 0]      # 起点坐标
  end_point: [25, 9]       # 终点坐标

# AGV配置
agv:
  max_count: 4        # 最大AGV数量
  max_capacity: 25    # 最大载重能力
  
  # 动作空间
  actions:
    - "UP"            # 上移 (0)
    - "DOWN"          # 下移 (1) 
    - "LEFT"          # 左移 (2)
    - "RIGHT"         # 右移 (3)
    - "WAIT"          # 等待 (4)

# 任务配置
tasks:
  max_count: 16       # 最大任务数量
  weights: [5, 10]    # 任务重量选项
  
  # 任务状态
  states:
    unassigned: 0     # 未分配
    assigned: 1       # 已分配
    completed: 2      # 已完成

# 课程学习配置
curriculum:
  stages:
    - stage: 1
      agv_num: 1
      task_num: 1
      max_steps: 500
      episodes: 800
      
    - stage: 2
      agv_num: 1
      task_num: 2
      max_steps: 800
      episodes: 1000
      
    - stage: 3
      agv_num: 1
      task_num: 4
      max_steps: 1200
      episodes: 1200
      
    - stage: 4
      agv_num: 2
      task_num: 4
      max_steps: 1500
      episodes: 1200
      
    - stage: 5
      agv_num: 2
      task_num: 6
      max_steps: 2000
      episodes: 1300
      
    - stage: 6
      agv_num: 2
      task_num: 8
      max_steps: 2500
      episodes: 1300
      
    - stage: 7
      agv_num: 3
      task_num: 8
      max_steps: 3000
      episodes: 1400
      
    - stage: 8
      agv_num: 3
      task_num: 10
      max_steps: 3500
      episodes: 1400
      
    - stage: 9
      agv_num: 3
      task_num: 12
      max_steps: 4000
      episodes: 1500
      
    - stage: 10
      agv_num: 4
      task_num: 12
      max_steps: 4500
      episodes: 1600
      
    - stage: 11
      agv_num: 4
      task_num: 14
      max_steps: 5000
      episodes: 1600
      
    - stage: 12
      agv_num: 4
      task_num: 16
      max_steps: 6000
      episodes: 1700

# 训练配置
training:
  total_episodes: 16000   # 总训练episodes
  test_episodes: 500      # 测试episodes
  
# 奖励配置
rewards:
  task_completion_base: 10.0     # 任务完成基础奖励 (增加正奖励)
  weight_bonus_factor: 1.0       # 重量加成系数 (增加奖励)
  movement_penalty: -0.05        # 移动惩罚 (减少惩罚)
  distance_penalty: -0.005       # 距离惩罚 (减少惩罚)
  conflict_penalty: -1.0         # 冲突惩罚 (减少惩罚)
  invalid_action_penalty: -0.5   # 无效动作惩罚 (减少惩罚)
  progress_reward: 0.1           # 接近目标奖励 (新增中间奖励)
  cooperation_bonus: 2.0         # 协作奖励 (新增多智能体协作奖励)

# 可视化配置
visualization:
  save_interval: 20              # 图表更新间隔(episodes)
  model_save_interval: 100       # 模型保存间隔(episodes)
  
  # 颜色配置
  colors:
    empty: [255, 255, 255]       # 空地 - 白色
    wall: [0, 0, 0]              # 墙壁 - 黑色
    shelf: [139, 69, 19]         # 货架 - 棕色
    agv: [0, 0, 255]             # AGV - 蓝色
    task: [255, 0, 0]            # 任务 - 红色
    start: [0, 255, 0]           # 起点 - 绿色
    end: [255, 165, 0]           # 终点 - 橙色
