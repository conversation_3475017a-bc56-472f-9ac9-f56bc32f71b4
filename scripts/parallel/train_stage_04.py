#!/usr/bin/env python3
"""
阶段4独立训练脚本
配置: 2个AGV, 4个任务, 复杂度8
目标episodes: 1200
"""

import os
import sys
import time
import torch
import ray
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.mappo.curriculum_trainer import CurriculumTrainer


def main():
    """执行阶段4训练"""
    print("🚀 阶段4独立训练启动")
    print("=" * 60)
    
    # 验证GPU环境
    print("🔧 GPU环境检查:")
    print(f"  PyTorch版本: {torch.__version__}")
    print(f"  CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"  GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"  GPU数量: {torch.cuda.device_count()}")
    print()
    
    # 阶段配置
    stage = 4
    agv_num = 2
    task_num = 4
    target_episodes = 1200
    complexity = 8
    
    print(f"📋 阶段4配置:")
    print(f"  AGV数量: 2")
    print(f"  任务数量: 4")
    print(f"  复杂度: 8")
    print(f"  目标episodes: 1200")
    print(f"  Ray端口: 6383")
    print(f"  仪表板端口: 8269")
    print()

    # 实验名称
    experiment_name = f"stage_4_parallel_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    config_path = "configs/mappo_config.yaml"
    
    print(f"  实验名称: {experiment_name}")
    print(f"  配置文件: {config_path}")
    print()
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 初始化Ray（使用独立端口避免冲突）
        if ray.is_initialized():
            ray.shutdown()
        
        print(f"🔧 初始化Ray集群 (端口: 6383)...")
        ray.init(
            address=None,  # 本地模式
            dashboard_port=8269,
            ignore_reinit_error=True,
            log_to_driver=False,
            _temp_dir=f"/tmp/ray_stage_4"
        )
        print("✅ Ray集群初始化完成")
        print()
        
        # 创建课程学习训练器
        print("🎓 初始化课程学习训练器...")
        trainer = CurriculumTrainer(
            config_path=config_path,
            experiment_name=experiment_name,
            enable_visualization=True
        )
        print("✅ 训练器初始化完成")
        print()
        
        # 执行单阶段训练
        print(f"🎯 开始阶段4训练...")
        print(f"  目标: 完成1200个episodes")
        print(f"  预计时间: {1200 * 5 / 3600:.1f} 小时")
        print()
        
        # 执行训练
        training_result = trainer.train_single_stage(
            stage=stage,
            target_episodes=1200
        )
        
        # 计算训练时间
        training_time = time.time() - start_time
        training_hours = training_time / 3600
        
        print()
        print("🎉 阶段4训练完成！")
        print("=" * 60)
        print(f"📊 训练结果:")
        print(f"  训练时长: {training_hours:.2f} 小时")
        print(f"  完成episodes: {training_result.get('total_episodes', 'N/A')}")
        print(f"  平均奖励: {training_result.get('avg_reward', 'N/A'):.2f}")
        print(f"  最终成功率: {training_result.get('final_success_rate', 'N/A'):.2f}")
        print(f"  结果目录: results/{experiment_name}")
        print()
        
        # 保存训练摘要
        summary = {
            "stage": stage,
            "agv_num": agv_num,
            "task_num": task_num,
            "complexity": complexity,
            "target_episodes": 1200,
            "actual_episodes": training_result.get('total_episodes', 0),
            "training_time_hours": training_hours,
            "avg_reward": training_result.get('avg_reward', 0),
            "final_success_rate": training_result.get('final_success_rate', 0),
            "experiment_name": experiment_name,
            "completion_time": datetime.now().isoformat()
        }
        
        summary_file = f"results/{experiment_name}/stage_4_summary.json"
        os.makedirs(os.path.dirname(summary_file), exist_ok=True)
        
        import json
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"📄 训练摘要已保存: {summary_file}")
        
    except Exception as e:
        print(f"❌ 阶段4训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        try:
            if 'trainer' in locals():
                print("🧹 清理训练器资源...")
                trainer.cleanup()
            
            print("🔧 关闭Ray集群...")
            ray.shutdown()
            print("✅ 资源清理完成")
            
        except Exception as e:
            print(f"⚠️ 资源清理时出错: {e}")
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
