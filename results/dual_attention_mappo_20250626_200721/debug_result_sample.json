{"timers": {"training_iteration": 7.290548646997195, "restore_env_runners": 1.175497891381383e-05, "training_step": 7.290319208987057, "env_runner_sampling_timer": 0.6977082249941304, "learner_update_timer": 6.587816410989035, "synch_weights": 0.004146727995248511}, "env_runners": {"module_to_env_connector": {"connector_pipeline_timer": 0.000281606509423576, "timers": {"connectors": {"normalize_and_clip_actions": 2.849045735308636e-05, "remove_single_ts_time_rank_from_batch": 1.2428092452100038e-06, "un_batch_to_individual_items": 1.4625746462732819e-05, "get_actions": 0.00012678155332489406, "module_to_agent_unmapping": 3.27185906669926e-06, "listify_data_for_vector_env": 6.061700074823081e-06, "tensor_to_numpy": 4.4529047341865346e-05}}}, "env_to_module_connector": {"timers": {"connectors": {"batch_individual_items": 1.523337155320333e-05, "agent_to_module_mapping": 4.189607187004991e-06, "add_states_from_episodes_to_batch": 2.6139439212898988e-06, "add_observations_from_episodes_to_batch": 1.6726572150796056e-05, "numpy_to_tensor": 2.4483967687769202e-05, "add_time_dim_to_batch_and_zero_pad": 4.909239652180087e-06}}, "connector_pipeline_timer": 0.00011079960348805652}, "episode_return_min": -110.42277777777784, "num_module_steps_sampled_lifetime": {"shared_policy": 3000.0}, "connector_pipeline_timer": 0.00047130900202319004, "timers": {"connectors": {"batch_individual_items": 4.325498593971133e-05, "agent_to_module_mapping": 1.0715011740103364e-05, "add_states_from_episodes_to_batch": 7.682014256715775e-06, "add_observations_from_episodes_to_batch": 5.713599966838956e-05, "numpy_to_tensor": 9.887799387797713e-05, "add_time_dim_to_batch_and_zero_pad": 2.3797008907422423e-05}}, "env_to_module_sum_episodes_length_in": 105.8467271109497, "num_episodes": 2.0, "num_module_steps_sampled": {"shared_policy": 1000.0}, "num_episodes_lifetime": 7.0, "num_agent_steps_sampled_lifetime": {"agv_0": 3000.0}, "episode_return_max": -13.1727777777778, "env_step_timer": 6.271291137771817e-05, "episode_len_min": 138, "module_episode_returns_mean": {"shared_policy": -40.958492063492095}, "env_reset_timer": 0.00016124697867780924, "env_to_module_sum_episodes_length_out": 105.8467271109497, "episode_duration_sec_mean": 0.1655403037272793, "num_env_steps_sampled_lifetime": 3000.0, "time_between_sampling": 0.0013853645319227869, "episode_len_max": 500, "weights_seq_no": 0.0, "num_agent_steps_sampled": {"agv_0": 1000.0}, "rlmodule_inference_timer": 0.00010296426708734131, "episode_return_mean": -40.958492063492095, "agent_episode_returns_mean": {"agv_0": -40.958492063492095}, "agent_steps": {"agv_0": 241.4285714285714}, "episode_len_mean": 241.4285714285714, "sample": 0.14113462568168322, "num_env_steps_sampled": 1000.0, "num_env_steps_sampled_lifetime_throughput": 1488.805401832055}, "learners": {"__all_modules__": {"num_env_steps_trained_lifetime": 315000, "learner_connector": {"timers": {"connectors": {"add_one_ts_to_episodes_and_truncate": 0.0015885290049482137, "add_time_dim_to_batch_and_zero_pad": 2.925499575212598e-05, "general_advantage_estimation": 0.058496791985817254, "batch_individual_items": 0.1564161340065766, "numpy_to_tensor": 0.0005275699950288981, "agent_to_module_mapping": 0.000688572006765753, "add_states_from_episodes_to_batch": 1.138600055128336e-05, "add_observations_from_episodes_to_batch": 8.96159908734262e-05, "add_columns_from_episodes_to_train_batch": 0.0177813479967881}}, "connector_pipeline_timer": 0.23612386599415913}, "num_env_steps_trained": 315000, "num_module_steps_trained": 10080, "learner_connector_sum_episodes_length_out": 1000, "learner_connector_sum_episodes_length_in": 1000, "num_trainable_parameters": 176646, "num_non_trainable_parameters": 0, "num_module_steps_trained_lifetime": 10080, "num_env_steps_trained_lifetime_throughput": 47379.032168992155, "num_module_steps_trained_throughput": 1516.1570270010673, "num_module_steps_trained_lifetime_throughput": 1516.1483492924472}, "shared_policy": {"vf_loss": "1.0347222", "curr_kl_coeff": 0.20000000298023224, "vf_loss_unclipped": "1.7000852", "total_loss": "0.5770702", "module_train_batch_size_mean": 32.0, "vf_explained_var": "0.08990687", "diff_num_grad_updates_vs_sampler_policy": "1.0", "mean_kl_loss": "0.019336276", "num_module_steps_trained": 10080, "gradients_default_optimizer_global_norm": "2.661901", "default_optimizer_learning_rate": 0.0001, "num_trainable_parameters": 176646, "weights_seq_no": 1.0, "curr_entropy_coeff": 0.05, "entropy": "1.5412157", "policy_loss": "0.13290258", "num_module_steps_trained_lifetime": 10080, "num_module_steps_trained_lifetime_throughput": 1516.166533802623}}, "num_training_step_calls_per_iteration": 1, "num_env_steps_sampled_lifetime": 3000.0, "fault_tolerance": {"num_healthy_workers": 0, "num_remote_worker_restarts": 0}, "env_runner_group": {"actor_manager_num_outstanding_async_reqs": 0}, "done": false, "training_iteration": 1, "trial_id": "default", "date": "2025-06-26_20-07-34", "timestamp": **********, "time_this_iter_s": 7.292799949645996, "time_total_s": 7.292799949645996, "pid": 2346689, "hostname": "521public", "node_ip": "*************", "config": {"exploration_config": {}, "extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 1, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "env": "multi_agent_agv_env", "env_config": {"curriculum_stage": 1, "env_config_path": "configs/environment_config.yaml", "render_mode": null}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": true, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 0, "create_local_env_runner": true, "num_envs_per_env_runner": 1, "gym_env_vectorize_mode": "SYNC", "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "episodes_to_numpy": true, "max_requests_in_flight_per_env_runner": 1, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "merge_env_runner_states": "training_only", "broadcast_env_runner_states": true, "episode_lookback_horizon": 1, "rollout_fragment_length": 200, "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "_is_online": true, "num_learners": 1, "num_gpus_per_learner": 1, "num_cpus_per_learner": "auto", "num_aggregator_actors_per_learner": 0, "max_requests_in_flight_per_aggregator_actor": 3, "local_gpu_idx": 0, "max_requests_in_flight_per_learner": 3, "gamma": 0.99, "lr": 0.0001, "grad_clip": 1.0, "grad_clip_by": "global_norm", "_train_batch_size_per_learner": null, "train_batch_size": 1000, "num_epochs": 10, "minibatch_size": 32, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {}, "_learner_class": null, "callbacks_on_algorithm_init": null, "callbacks_on_env_runners_recreated": null, "callbacks_on_offline_eval_runners_recreated": null, "callbacks_on_checkpoint_loaded": null, "callbacks_on_environment_created": null, "callbacks_on_episode_created": null, "callbacks_on_episode_start": null, "callbacks_on_episode_step": null, "callbacks_on_episode_end": null, "callbacks_on_evaluate_start": null, "callbacks_on_evaluate_end": null, "callbacks_on_evaluate_offline_start": null, "callbacks_on_evaluate_offline_end": null, "callbacks_on_sample_end": null, "callbacks_on_train_result": null, "explore": true, "enable_rl_module_and_learner": true, "enable_env_runner_and_connector_v2": true, "_prior_exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function MAPPOConfig.create_multi_agent_config.<locals>.policy_mapping_fn at 0x7fd60f2e65c0>", "policies_to_train": ["shared_policy"], "policy_states_are_swappable": false, "observation_fn": null, "offline_data_class": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "ignore_final_observation": false, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_remaining_data": false, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": 20, "evaluation_duration": 5, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_auto_duration_min_env_steps_per_sample": 100, "evaluation_auto_duration_max_env_steps_per_sample": 2000, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "offline_evaluation_interval": null, "num_offline_eval_runners": 0, "offline_loss_for_module_fn": null, "offline_evaluation_duration": 1, "offline_evaluation_parallel_to_training": false, "offline_evaluation_timeout_s": 120.0, "num_cpus_per_offline_eval_runner": 1, "num_gpus_per_offline_eval_runner": 0, "custom_resources_per_offline_eval_runner": {}, "restart_failed_offline_eval_runners": true, "ignore_offline_eval_runner_failures": false, "max_num_offline_eval_runner_restarts": 1000, "offline_eval_runner_restore_timeout_s": 1800.0, "max_requests_in_flight_per_offline_eval_runner": 1, "validate_offline_eval_runners_after_construction": true, "offline_eval_runner_health_probe_timeout_s": 30.0, "offline_eval_rl_module_inference_only": false, "broadcast_offline_eval_runner_states": false, "offline_eval_batch_size_per_runner": 256, "dataset_num_iters_per_eval_runner": 1, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_validate_config": true, "_use_msgpack_checkpoints": false, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "env_task_fn": -1, "enable_connectors": -1, "simple_optimizer": true, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 0.5, "entropy_coeff": 0.05, "clip_param": 0.2, "vf_clip_param": 10.0, "entropy_coeff_schedule": null, "lr_schedule": null, "sgd_minibatch_size": -1, "vf_share_layers": -1, "class": "<class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>", "lambda": 0.95, "input": "sampler", "policies": {"shared_policy": [null, "Box(-inf, inf, (84,), float32)", "Discrete(5)", {"model": {"custom_model": "attention_policy", "custom_model_config": {"max_agvs": 4, "max_tasks": 16, "agv_feature_dim": 5, "task_feature_dim": 4, "attention_config_path": "configs/attention_config.yaml"}}}]}, "callbacks": "<class 'ray.rllib.callbacks.callbacks.RLlibCallback'>", "create_env_on_driver": false, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 7.292799949645996, "iterations_since_restore": 1, "perf": {"cpu_util_percent": 51.31818181818182, "ram_util_percent": 83.77272727272727}}