# MAPPO算法配置文件

# 算法超参数配置
algorithm:
  learning_rate: 0.0001        # 学习率 (降低以提高稳定性)
  gamma: 0.99                  # 折扣因子
  lambda: 0.95                 # GAE参数
  clip_param: 0.2              # PPO裁剪参数
  vf_clip_param: 10.0          # 价值函数裁剪参数
  entropy_coeff: 0.05          # 熵系数 (增加探索)
  vf_loss_coeff: 0.5           # 价值函数损失系数
  kl_coeff: 0.2                # KL散度系数
  kl_target: 0.01              # KL散度目标
  grad_clip: 1.0               # 梯度裁剪 (放宽限制)
  use_gae: true                # 使用GAE
  use_critic: true             # 使用价值函数

# 训练配置
training:
  train_batch_size: 1000       # 训练批大小 (进一步减小提高稳定性)
  sgd_minibatch_size: 32       # SGD小批大小 (减小提高稳定性)
  num_sgd_iter: 10             # SGD迭代次数 (增加学习次数)
  rollout_fragment_length: 200 # 采样片段长度 (增加以获得更多经验)
  batch_mode: "truncate_episodes"  # 批处理模式
  num_workers: 0               # 工作进程数 (设为0使用单进程模式)
  num_envs_per_worker: 1       # 每个工作进程的环境数
  evaluation_interval: 20      # 评估间隔 (增加以减少开销)
  evaluation_duration: 5       # 评估持续时间 (减少以降低负载)
  evaluation_num_workers: 0    # 评估工作进程数 (禁用独立评估worker)
  num_gpus: 1                  # 使用GPU数量
  num_gpus_per_worker: 0       # 每个worker使用的GPU数量
  sample_timeout_s: 120        # 采样超时时间 (增加超时时间)
  recreate_failed_workers: true # 自动重建失败的worker

# 环境配置
environment:
  curriculum_stage: 1          # 课程学习阶段
  env_config_path: "configs/environment_config.yaml"  # 环境配置文件路径
  render_mode: null            # 渲染模式

# 模型配置
model:
  max_agvs: 4                  # 最大AGV数量
  max_tasks: 16                # 最大任务数量
  attention_config_path: "configs/attention_config.yaml"  # 注意力配置文件路径

# 课程学习配置
curriculum_learning:
  total_episodes: 16000        # 总训练episodes
  test_episodes: 500           # 测试episodes
  stage_transition_threshold: 0.8  # 阶段转换阈值（成功率）
  min_episodes_per_stage: 100 # 每阶段最少episodes
  max_episodes_per_stage: 2000 # 每阶段最多episodes

# 监控和日志配置
monitoring:
  log_level: "INFO"            # 日志级别
  tensorboard_log: true        # 启用TensorBoard日志
  wandb_log: false             # 启用Weights & Biases日志
  save_interval: 100           # 模型保存间隔
  checkpoint_frequency: 10     # 检查点保存频率

# 可视化配置
visualization:
  save_attention_maps: true    # 保存注意力图
  save_training_curves: true   # 保存训练曲线
  update_interval: 20          # 图表更新间隔
  plot_format: "png"           # 图表格式

# 实验配置
experiment:
  name: "attention_mappo_agv"  # 实验名称
  description: "双层注意力机制MAPPO多AGV调度"  # 实验描述
  tags: ["attention", "mappo", "agv", "multi-agent"]  # 实验标签
  save_results: true           # 保存实验结果
  results_dir: "results"       # 结果保存目录