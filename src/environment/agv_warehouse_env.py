"""
AGV仓储环境
基于Gymnasium接口的多智能体强化学习环境
支持课程学习和动态配置
"""

import gymnasium as gym
from gymnasium import spaces
import numpy as np
import yaml
import random
from typing import Dict, List, Tuple, Optional, Any
import copy

from .map_generator import MapGenerator
from .agv import AGV, Task, Action
from .pathfinding import AStarPathfinder

class AGVWarehouseEnv(gym.Env):
    """AGV仓储环境"""
    
    metadata = {"render_modes": ["human", "rgb_array"], "render_fps": 4}
    
    def __init__(self, config_path: str = "configs/environment_config.yaml", 
                 curriculum_stage: int = 1, render_mode: Optional[str] = None):
        """
        初始化环境
        
        Args:
            config_path: 配置文件路径
            curriculum_stage: 课程学习阶段 (1-12)
            render_mode: 渲染模式
        """
        super().__init__()
        
        # 加载配置
        self.config = self._load_config(config_path)
        self.curriculum_stage = curriculum_stage
        self.render_mode = render_mode
        
        # 获取当前阶段配置
        self.stage_config = self._get_stage_config(curriculum_stage)
        self.num_agvs = self.stage_config['agv_num']
        self.num_tasks = self.stage_config['task_num']
        self.max_steps = self.stage_config['max_steps']
        
        # 初始化地图生成器
        self.map_generator = MapGenerator(config_path)
        self.map_grid = self.map_generator.generate_map()
        
        # 初始化路径规划器
        self.pathfinder = AStarPathfinder(self.map_grid)
        
        # 获取有效位置
        self.valid_positions = self.map_generator.get_valid_positions(self.map_grid)
        self.shelf_adjacent_positions = self.map_generator.get_shelf_adjacent_positions(self.map_grid)
        
        # 环境状态
        self.agvs: List[AGV] = []
        self.tasks: List[Task] = []
        self.current_step = 0
        
        # 统计信息
        self.episode_stats = {
            'total_reward': 0.0,
            'tasks_completed': 0,
            'total_distance': 0,
            'collision_count': 0,
            'load_utilization': 0.0
        }
        
        # 定义动作和观察空间
        self._setup_spaces()
        
        # 奖励配置
        self.reward_config = self.config['rewards']
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _get_stage_config(self, stage: int) -> Dict:
        """获取课程学习阶段配置"""
        stages = self.config['curriculum']['stages']
        for stage_config in stages:
            if stage_config['stage'] == stage:
                return stage_config
        raise ValueError(f"无效的课程学习阶段: {stage}")
    
    def _setup_spaces(self):
        """设置动作空间和观察空间"""
        # 动作空间：每个AGV有5个动作
        self.action_space = spaces.MultiDiscrete([5] * self.num_agvs)
        
        # 观察空间：全局状态表示
        # 任务状态：16个任务 × 4个特征 (x, y, weight, state)
        # AGV状态：4个AGV × 5个特征 (x, y, load, capacity, queue_length)
        max_tasks = self.config['tasks']['max_count']
        max_agvs = self.config['agv']['max_count']
        
        # 任务观察空间
        task_low = np.array([0, 0, 5, 0] * max_tasks, dtype=np.float32)
        task_high = np.array([25, 9, 10, 2] * max_tasks, dtype=np.float32)
        
        # AGV观察空间
        agv_low = np.array([0, 0, 0, 25, 0] * max_agvs, dtype=np.float32)
        agv_high = np.array([25, 9, 25, 25, 16] * max_agvs, dtype=np.float32)
        
        # 合并观察空间
        obs_low = np.concatenate([task_low, agv_low])
        obs_high = np.concatenate([task_high, agv_high])
        
        self.observation_space = spaces.Box(
            low=obs_low,
            high=obs_high,
            dtype=np.float32
        )
    
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[np.ndarray, Dict]:
        """
        重置环境
        
        Args:
            seed: 随机种子
            options: 重置选项
            
        Returns:
            Tuple[np.ndarray, Dict]: 初始观察和信息
        """
        super().reset(seed=seed)
        
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
        
        # 重置环境状态
        self.current_step = 0
        self.agvs.clear()
        self.tasks.clear()
        
        # 重置统计信息
        self.episode_stats = {
            'total_reward': 0.0,
            'tasks_completed': 0,
            'total_distance': 0,
            'collision_count': 0,
            'load_utilization': 0.0
        }
        
        # 初始化AGV
        self._initialize_agvs()
        
        # 初始化任务
        self._initialize_tasks()
        
        # 获取初始观察
        observation = self._get_observation()
        info = self._get_info()
        
        return observation, info
    
    def _initialize_agvs(self):
        """初始化AGV"""
        # 选择AGV起始位置
        start_positions = random.sample(self.valid_positions, self.num_agvs)
        
        for i in range(self.num_agvs):
            x, y = start_positions[i]
            agv = AGV(agv_id=i, x=x, y=y, max_capacity=self.config['agv']['max_capacity'])
            self.agvs.append(agv)
    
    def _initialize_tasks(self):
        """初始化任务"""
        # 获取所有货架位置
        shelf_positions = []
        for y in range(self.map_grid.shape[0]):
            for x in range(self.map_grid.shape[1]):
                if self.map_grid[y, x] == 2:  # 货架位置
                    shelf_positions.append((x, y))

        # 选择任务位置（直接在货架上）
        task_positions = random.sample(shelf_positions, self.num_tasks)
        task_weights = self.config['tasks']['weights']

        for i in range(self.num_tasks):
            x, y = task_positions[i]
            weight = random.choice(task_weights)
            task = Task(id=i, x=x, y=y, weight=weight, state=0)
            self.tasks.append(task)

    def step(self, actions: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, Dict]:
        """
        执行一步动作

        Args:
            actions: 所有AGV的动作数组

        Returns:
            Tuple: (observation, reward, terminated, truncated, info)
        """
        self.current_step += 1

        # 执行动作
        rewards = self._execute_actions(actions)

        # 计算总奖励
        total_reward = sum(rewards)
        self.episode_stats['total_reward'] += total_reward

        # 检查终止条件
        terminated = self._check_terminated()
        truncated = self.current_step >= self.max_steps

        # 更新统计信息
        self._update_stats()

        # 获取观察和信息
        observation = self._get_observation()
        info = self._get_info()

        return observation, total_reward, terminated, truncated, info

    def _execute_actions(self, actions: np.ndarray) -> List[float]:
        """
        执行所有AGV的动作

        Args:
            actions: 动作数组

        Returns:
            List[float]: 每个AGV的奖励
        """
        rewards = [0.0] * self.num_agvs
        new_positions = []

        # 计算所有AGV的新位置
        for i, agv in enumerate(self.agvs):
            action = Action(actions[i])
            new_x, new_y = agv.move(action)

            # 检查位置有效性
            if self.map_generator.is_valid_position(self.map_grid, new_x, new_y):
                new_positions.append((new_x, new_y))
            else:
                # 无效移动，保持原位置
                new_positions.append((agv.x, agv.y))
                rewards[i] += self.reward_config['invalid_action_penalty']

        # 检查碰撞
        collision_pairs = self._check_collisions(new_positions)

        # 执行移动和计算奖励
        for i, agv in enumerate(self.agvs):
            action = Action(actions[i])
            new_x, new_y = new_positions[i]

            # 检查是否发生碰撞
            if i in collision_pairs:
                # 发生碰撞，不移动
                rewards[i] += self.reward_config['conflict_penalty']
                agv.collision_count += 1
                self.episode_stats['collision_count'] += 1
            else:
                # 正常移动
                agv.execute_move(action, new_x, new_y)

                # 移动奖励
                if action != Action.WAIT:
                    rewards[i] += self.reward_config['movement_penalty']
                    distance = abs(new_x - agv.x) + abs(new_y - agv.y)
                    rewards[i] += self.reward_config['distance_penalty'] * distance

            # 检查任务完成
            task_reward = self._check_task_completion(agv)
            rewards[i] += task_reward

            # 添加距离引导奖励
            distance_reward = self._calculate_distance_reward(agv)
            rewards[i] += distance_reward

        return rewards

    def _check_collisions(self, positions: List[Tuple[int, int]]) -> List[int]:
        """
        检查AGV碰撞

        Args:
            positions: 所有AGV的新位置

        Returns:
            List[int]: 发生碰撞的AGV索引列表
        """
        collision_agvs = []

        for i in range(len(positions)):
            for j in range(i + 1, len(positions)):
                if positions[i] == positions[j]:
                    collision_agvs.extend([i, j])

        return list(set(collision_agvs))

    def _check_task_completion(self, agv: AGV) -> float:
        """
        检查AGV是否完成任务

        Args:
            agv: AGV对象

        Returns:
            float: 任务完成奖励
        """
        reward = 0.0
        agv_pos = (agv.x, agv.y)

        # 检查AGV是否在货架邻近位置，且邻近位置有未完成任务
        for task in self.tasks:
            if task.state == 0:  # 未完成任务
                task_pos = (task.x, task.y)

                # 检查AGV是否在任务货架的邻近位置
                if self._is_adjacent_to_task(agv_pos, task_pos):
                    # 尝试分配任务
                    if agv.assign_task(task):
                        # 立即完成任务（简化版本）
                        agv.complete_task(task)

                        # 计算奖励
                        base_reward = self.reward_config['task_completion_base']
                        weight_bonus = self.reward_config['weight_bonus_factor'] * task.weight
                        reward += base_reward + weight_bonus

                        self.episode_stats['tasks_completed'] += 1
                        break

        return reward

    def _calculate_distance_reward(self, agv: AGV) -> float:
        """
        计算距离引导奖励，鼓励AGV向最近的未完成任务移动

        Args:
            agv: AGV对象

        Returns:
            float: 距离引导奖励
        """
        if not self.tasks:
            return 0.0

        agv_pos = (agv.x, agv.y)

        # 找到最近的未完成任务
        min_distance = float('inf')
        for task in self.tasks:
            if task.state == 0:  # 未完成任务
                task_pos = (task.x, task.y)
                distance = abs(agv_pos[0] - task_pos[0]) + abs(agv_pos[1] - task_pos[1])
                min_distance = min(min_distance, distance)

        if min_distance == float('inf'):
            return 0.0

        # 距离越近，奖励越高（但奖励很小，避免干扰主要奖励）
        max_distance = self.map_grid.shape[0] + self.map_grid.shape[1]  # 地图对角线距离
        normalized_distance = min_distance / max_distance
        distance_reward = (1.0 - normalized_distance) * 0.01  # 最大0.01的引导奖励

        return distance_reward

    def _is_adjacent_to_task(self, agv_pos: Tuple[int, int], task_pos: Tuple[int, int]) -> bool:
        """
        检查AGV是否在任务的邻近位置

        Args:
            agv_pos: AGV位置
            task_pos: 任务位置（货架位置）

        Returns:
            bool: 是否邻近
        """
        agv_x, agv_y = agv_pos
        task_x, task_y = task_pos

        # 检查四个方向的邻近位置
        adjacent_positions = [
            (task_x + 1, task_y),  # 右
            (task_x - 1, task_y),  # 左
            (task_x, task_y + 1),  # 下
            (task_x, task_y - 1),  # 上
        ]

        return (agv_x, agv_y) in adjacent_positions

    def _check_terminated(self) -> bool:
        """检查是否终止"""
        # 所有任务完成
        completed_tasks = sum(1 for task in self.tasks if task.state == 2)
        return completed_tasks == self.num_tasks

    def _update_stats(self):
        """更新统计信息"""
        # 计算总距离
        total_distance = sum(agv.total_distance for agv in self.agvs)
        self.episode_stats['total_distance'] = total_distance

        # 计算载重利用率
        if self.num_agvs > 0:
            avg_utilization = sum(agv.get_load_utilization() for agv in self.agvs) / self.num_agvs
            self.episode_stats['load_utilization'] = avg_utilization

    def _get_observation(self) -> np.ndarray:
        """
        获取环境观察

        Returns:
            np.ndarray: 观察向量
        """
        # 任务状态
        task_obs = []
        max_tasks = self.config['tasks']['max_count']

        for i in range(max_tasks):
            if i < len(self.tasks):
                task_obs.extend(self.tasks[i].to_array())
            else:
                # 填充空任务
                task_obs.extend([0, 0, 0, 0])

        # AGV状态
        agv_obs = []
        max_agvs = self.config['agv']['max_count']

        for i in range(max_agvs):
            if i < len(self.agvs):
                agv_obs.extend(self.agvs[i].to_array())
            else:
                # 填充空AGV
                agv_obs.extend([0, 0, 0, 0, 0])

        # 合并观察
        observation = np.array(task_obs + agv_obs, dtype=np.float32)
        return observation

    def _get_info(self) -> Dict:
        """获取环境信息"""
        return {
            'current_step': self.current_step,
            'curriculum_stage': self.curriculum_stage,
            'num_agvs': self.num_agvs,
            'num_tasks': self.num_tasks,
            'episode_stats': self.episode_stats.copy(),
            'agv_states': [agv.get_state_dict() for agv in self.agvs],
            'task_states': [(task.id, task.x, task.y, task.weight, task.state) for task in self.tasks]
        }

    def render(self):
        """渲染环境（简化版本）"""
        if self.render_mode == "human":
            print(f"\n=== Step {self.current_step} ===")
            print(f"Stage: {self.curriculum_stage}, AGVs: {self.num_agvs}, Tasks: {self.num_tasks}")

            # 显示AGV状态
            for agv in self.agvs:
                print(f"AGV {agv.id}: pos=({agv.x},{agv.y}), load={agv.current_load}/{agv.max_capacity}")

            # 显示任务状态
            completed = sum(1 for task in self.tasks if task.state == 2)
            print(f"Tasks completed: {completed}/{self.num_tasks}")
            print(f"Episode stats: {self.episode_stats}")

    def close(self):
        """关闭环境"""
        pass

    def get_curriculum_config(self) -> Dict:
        """获取课程学习配置"""
        return self.config['curriculum']

    def set_curriculum_stage(self, stage: int):
        """
        设置课程学习阶段

        Args:
            stage: 新的阶段编号
        """
        if 1 <= stage <= 12:
            self.curriculum_stage = stage
            self.stage_config = self._get_stage_config(stage)
            self.num_agvs = self.stage_config['agv_num']
            self.num_tasks = self.stage_config['task_num']
            self.max_steps = self.stage_config['max_steps']

            # 重新设置动作空间
            self.action_space = spaces.MultiDiscrete([5] * self.num_agvs)
        else:
            raise ValueError(f"无效的课程学习阶段: {stage}")

if __name__ == "__main__":
    # 测试AGV仓储环境
    print("🏭 测试AGV仓储环境...")

    # 创建环境
    env = AGVWarehouseEnv(curriculum_stage=1, render_mode="human")

    print(f"动作空间: {env.action_space}")
    print(f"观察空间: {env.observation_space}")

    # 重置环境
    obs, info = env.reset(seed=42)
    print(f"初始观察形状: {obs.shape}")
    print(f"初始信息: {info}")

    # 执行几步
    for step in range(5):
        # 随机动作
        actions = env.action_space.sample()
        obs, reward, terminated, truncated, info = env.step(actions)

        env.render()
        print(f"动作: {actions}, 奖励: {reward:.2f}")

        if terminated or truncated:
            break

    print("✅ AGV仓储环境测试完成！")
