# 双层注意力机制配置文件

# 注意力网络基础配置
attention:
  name: "DualLayerAttention"
  version: "1.0.0"
  
  # 特征维度配置
  dimensions:
    agv_raw_features: 5        # AGV原始特征维度 [x, y, load, capacity, queue_length]
    task_raw_features: 4       # 任务原始特征维度 [x, y, weight, state]
    hidden_dim: 64             # 隐藏层维度
    attention_dim: 64          # 注意力机制维度
    output_dim: 64             # 输出特征维度

# 第一层：AGV-任务匹配注意力
layer1_agv_task_attention:
  name: "AGVTaskAttention"
  
  # 网络结构
  architecture:
    agv_encoder_layers: [64, 64]      # AGV特征编码器
    task_encoder_layers: [64, 64]     # 任务特征编码器
    attention_heads: 4                # 多头注意力头数
    dropout_rate: 0.1                 # Dropout率
    
  # 注意力计算参数
  attention_params:
    temperature: 1.0                  # 注意力温度参数
    use_layer_norm: true              # 是否使用层归一化
    use_residual: true                # 是否使用残差连接
    
  # 掩码配置
  masking:
    enable_capacity_mask: true        # 启用载重能力掩码
    enable_distance_mask: true        # 启用距离掩码
    enable_task_state_mask: true      # 启用任务状态掩码
    max_distance_threshold: 10        # 最大距离阈值
    
  # 权重融合
  fusion:
    method: "weighted_sum"            # 融合方法
    learnable_weights: true           # 是否学习融合权重

# 第二层：AGV协作注意力
layer2_agv_collaboration:
  name: "AGVCollaborationAttention"
  
  # 网络结构
  architecture:
    agv_encoder_layers: [64, 64]      # AGV特征编码器
    attention_heads: 4                # 多头注意力头数
    dropout_rate: 0.1                 # Dropout率
    
  # 注意力计算参数
  attention_params:
    temperature: 1.0                  # 注意力温度参数
    use_layer_norm: true              # 是否使用层归一化
    use_residual: true                # 是否使用残差连接
    
  # 掩码配置
  masking:
    enable_self_mask: true            # 启用自注意力掩码（AGV不关注自己）
    enable_distance_mask: true        # 启用距离掩码
    enable_load_mask: true            # 启用载重掩码
    collaboration_radius: 8           # 协作半径
    
  # 协作特征
  collaboration_features:
    include_relative_position: true   # 包含相对位置
    include_load_difference: true     # 包含载重差异
    include_task_overlap: true        # 包含任务重叠度

# 特征融合配置
feature_fusion:
  name: "DualLayerFusion"
  
  # 融合策略
  strategy:
    method: "weighted_concatenation"  # 加权拼接
    layer1_weight: 0.6               # 第一层权重
    layer2_weight: 0.4               # 第二层权重
    learnable_weights: true          # 是否学习权重
    
  # 输出处理
  output_processing:
    use_final_mlp: true              # 使用最终MLP
    final_mlp_layers: [128, 64]      # 最终MLP层
    activation: "relu"               # 激活函数
    use_batch_norm: true             # 使用批归一化

# 训练配置
training:
  # 优化器配置
  optimizer:
    type: "adam"
    learning_rate: 0.0003
    weight_decay: 0.0001
    
  # 学习率调度
  lr_scheduler:
    type: "cosine_annealing"
    T_max: 1000
    eta_min: 0.00001
    
  # 正则化
  regularization:
    attention_entropy_weight: 0.01   # 注意力熵正则化权重
    feature_diversity_weight: 0.001  # 特征多样性权重
    
  # 梯度处理
  gradient:
    clip_norm: 1.0                   # 梯度裁剪
    accumulation_steps: 1            # 梯度累积步数

# 评估配置
evaluation:
  # 注意力分析
  attention_analysis:
    save_attention_maps: true        # 保存注意力图
    compute_attention_entropy: true  # 计算注意力熵
    analyze_attention_patterns: true # 分析注意力模式
    
  # 特征分析
  feature_analysis:
    compute_feature_similarity: true # 计算特征相似度
    analyze_feature_distribution: true # 分析特征分布
    save_feature_embeddings: true   # 保存特征嵌入

# 可视化配置
visualization:
  # 注意力可视化
  attention_visualization:
    heatmap_resolution: [26, 10]     # 热力图分辨率
    color_scheme: "viridis"          # 颜色方案
    save_format: "png"               # 保存格式
    
  # 特征可视化
  feature_visualization:
    use_tsne: true                   # 使用t-SNE降维
    use_pca: true                    # 使用PCA降维
    perplexity: 30                   # t-SNE困惑度
    
# 调试配置
debug:
  enable_debug_mode: false          # 启用调试模式
  log_attention_weights: false      # 记录注意力权重
  log_gradient_norms: false         # 记录梯度范数
  save_intermediate_features: false # 保存中间特征
