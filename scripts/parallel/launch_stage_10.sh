#!/bin/bash
# 阶段10独立启动脚本
# 配置: 4个AGV, 12个任务, 复杂度48

echo "🚀 启动阶段10训练"
echo "配置: 4个AGV, 12个任务, 复杂度48"
echo "目标episodes: 1600"
echo ""

# 检查环境
if [[ "$CONDA_DEFAULT_ENV" != "biye_RL" ]]; then
    echo "❌ 请先激活biye_RL环境: conda activate biye_RL"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动训练
echo "开始训练..."
python scripts/parallel/train_stage_10.py 2>&1 | tee logs/stage_10_$(date +%Y%m%d_%H%M%S).log

echo "训练完成！"
