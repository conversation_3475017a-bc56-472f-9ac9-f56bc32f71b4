#!/usr/bin/env python3
"""
超参数调优训练脚本
支持结果文件夹命名格式：脚本名+日期
在biye_RL conda环境中运行，使用GPU加速
"""

import os
import sys
import time
import json
import torch
import argparse
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.mappo.curriculum_trainer import CurriculumTrainer


def create_experiment_name(script_name, custom_suffix=None):
    """创建实验名称：脚本名+日期"""
    date_str = datetime.now().strftime('%Y%m%d')
    base_name = f"{script_name}_{date_str}"
    
    if custom_suffix:
        return f"{base_name}_{custom_suffix}"
    else:
        return base_name


def run_training_experiment(config_path, experiment_name, max_episodes=16000, 
                          enable_visualization=True, start_stage=1, end_stage=12):
    """执行训练实验"""
    print(f"🚀 开始训练实验: {experiment_name}")
    print("=" * 80)
    
    # 验证GPU环境
    print("🔧 GPU环境检查:")
    print(f"  PyTorch版本: {torch.__version__}")
    print(f"  CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"  GPU设备: {torch.cuda.get_device_name(0)}")
        print(f"  GPU数量: {torch.cuda.device_count()}")
    print()
    
    print(f"📋 实验配置:")
    print(f"  实验名称: {experiment_name}")
    print(f"  配置文件: {config_path}")
    print(f"  目标episodes: {max_episodes}")
    print(f"  课程学习阶段: {start_stage}-{end_stage}")
    print(f"  可视化监控: {'启用' if enable_visualization else '禁用'}")
    print(f"  GPU加速: {'启用' if torch.cuda.is_available() else '禁用'}")
    print()
    
    # 记录实验开始时间
    experiment_start_time = time.time()
    
    try:
        # 创建课程学习训练器
        print("🎓 初始化课程学习训练器...")
        trainer = CurriculumTrainer(
            config_path=config_path,
            experiment_name=experiment_name,
            enable_visualization=enable_visualization
        )
        print("✅ 训练器初始化完成")
        print()
        
        # 执行完整的课程学习训练
        print(f"🎯 开始执行课程学习训练 (阶段{start_stage}-{end_stage})...")
        training_result = trainer.train_curriculum(
            start_stage=start_stage,
            end_stage=end_stage
        )
        
        # 记录实验结束时间
        experiment_end_time = time.time()
        total_experiment_time = experiment_end_time - experiment_start_time
        
        # 输出实验结果摘要
        print("\n" + "=" * 80)
        print("🎉 训练实验执行完成！")
        print("=" * 80)
        print(f"📊 实验结果摘要:")
        print(f"  实验名称: {training_result['experiment_name']}")
        print(f"  总训练时长: {total_experiment_time:.1f}秒 ({total_experiment_time/3600:.2f}小时)")
        print(f"  总episodes: {training_result['total_episodes']}")
        print(f"  完成阶段: {training_result['stages_completed']}")
        print(f"  最终阶段: {training_result['final_stage']}")
        
        if 'final_performance' in training_result:
            perf = training_result['final_performance']
            print(f"  最终性能:")
            print(f"    平均奖励: {perf.get('avg_reward', 0):.3f}")
            print(f"    成功率: {perf.get('success_rate', 0):.3f}")
            print(f"    平均步长: {perf.get('avg_episode_length', 0):.1f}")
        
        # 保存实验摘要
        experiment_summary = {
            "experiment_type": "hyperparameter_training",
            "algorithm": "dual_attention_mappo",
            "experiment_name": experiment_name,
            "config_path": config_path,
            "start_time": experiment_start_time,
            "end_time": experiment_end_time,
            "total_duration": total_experiment_time,
            "max_episodes": max_episodes,
            "start_stage": start_stage,
            "end_stage": end_stage,
            "enable_visualization": enable_visualization,
            "training_result": training_result,
            "success": True
        }
        
        summary_path = os.path.join("results", experiment_name, "experiment_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(experiment_summary, f, indent=2, ensure_ascii=False)
        
        print(f"  实验摘要保存至: {summary_path}")
        print()
        
        return True, training_result
        
    except Exception as e:
        print(f"❌ 训练实验执行失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 保存错误信息
        error_summary = {
            "experiment_type": "hyperparameter_training",
            "algorithm": "dual_attention_mappo",
            "experiment_name": experiment_name,
            "config_path": config_path,
            "start_time": experiment_start_time,
            "end_time": time.time(),
            "max_episodes": max_episodes,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "success": False
        }
        
        error_path = os.path.join("results", experiment_name, "experiment_error.json")
        os.makedirs(os.path.dirname(error_path), exist_ok=True)
        with open(error_path, 'w', encoding='utf-8') as f:
            json.dump(error_summary, f, indent=2, ensure_ascii=False)
        
        return False, None
        
    finally:
        # 清理资源
        if 'trainer' in locals():
            print("🧹 清理训练器资源...")
            trainer.cleanup()
            print("✅ 资源清理完成")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='超参数调优训练脚本')
    parser.add_argument('--config', type=str, default='configs/mappo_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--episodes', type=int, default=16000,
                       help='最大训练episodes数')
    parser.add_argument('--suffix', type=str, default=None,
                       help='实验名称后缀')
    parser.add_argument('--no-viz', action='store_true',
                       help='禁用可视化监控')
    parser.add_argument('--start-stage', type=int, default=1,
                       help='开始阶段')
    parser.add_argument('--end-stage', type=int, default=12,
                       help='结束阶段')
    
    args = parser.parse_args()
    
    # 创建实验名称
    script_name = "hyperparameter_training"
    experiment_name = create_experiment_name(script_name, args.suffix)
    
    # 执行训练实验
    success, result = run_training_experiment(
        config_path=args.config,
        experiment_name=experiment_name,
        max_episodes=args.episodes,
        enable_visualization=not args.no_viz,
        start_stage=args.start_stage,
        end_stage=args.end_stage
    )
    
    # 输出最终结果
    exit_code = 0 if success else 1
    print(f"\n🏁 训练实验{'成功' if success else '失败'}，退出码: {exit_code}")
    
    if success and result:
        print("\n📋 下一步建议:")
        print("  1. 检查训练结果和可视化图表")
        print("  2. 分析性能指标和学习曲线")
        print("  3. 进行超参数调优实验")
        print("  4. 对比不同配置的性能")
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
