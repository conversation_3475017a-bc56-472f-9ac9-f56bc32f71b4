#!/bin/bash
# 12阶段并行训练批量启动脚本

echo "🚀 启动12阶段并行训练"
echo "=============================="

# 检查conda环境
if [[ "$CONDA_DEFAULT_ENV" != "biye_RL" ]]; then
    echo "❌ 请先激活biye_RL环境: conda activate biye_RL"
    exit 1
fi

# 检查GPU可用性
echo "🔧 GPU环境检查:"
python -c "import torch; print(f'GPU可用: {torch.cuda.is_available()}')"

echo ""
echo "📋 将启动12个训练进程..."
echo ""

# 创建日志目录
mkdir -p logs

# 启动所有训练脚本
for stage in {01..12}; do
    echo "启动阶段${stage}训练..."
    
    # 使用gnome-terminal在新窗口中启动
    gnome-terminal --title="阶段${stage}训练" -- bash -c "
        cd $(pwd) && 
        conda activate biye_RL && 
        python scripts/parallel/train_stage_${stage}.py 2>&1 | tee logs/stage_${stage}_$(date +%Y%m%d_%H%M%S).log
        echo '训练完成，按Enter关闭...'
        read
    " &
    
    # 等待2秒避免同时启动造成冲突
    sleep 2
done

echo ""
echo "✅ 所有训练进程已启动"
echo "💡 提示:"
echo "  - 每个阶段在独立终端中运行"
echo "  - 日志保存在logs/目录中"
echo "  - 结果保存在results/目录中"
echo "  - 使用 'ps aux | grep python' 查看运行状态"
echo "  - 使用 'pkill -f train_stage' 停止所有训练"
