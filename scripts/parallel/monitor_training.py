#!/usr/bin/env python3
# 训练进程监控脚本

import os
import time
import subprocess
from datetime import datetime

def check_training_processes():
    """检查训练进程状态"""
    print(f"🔍 训练进程监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 检查Python训练进程
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        training_processes = []
        for line in lines:
            if 'train_stage_' in line and 'python' in line:
                training_processes.append(line)
        
        if training_processes:
            print(f"📊 发现 {len(training_processes)} 个训练进程:")
            for i, process in enumerate(training_processes, 1):
                parts = process.split()
                if len(parts) >= 11:
                    pid = parts[1]
                    cpu = parts[2]
                    mem = parts[3]
                    cmd = ' '.join(parts[10:])
                    print(f"  {i}. PID:{pid} CPU:{cpu}% MEM:{mem}% - {cmd}")
        else:
            print("❌ 未发现训练进程")
        
        print()
        
        # 检查GPU使用情况
        try:
            gpu_result = subprocess.run(['nvidia-smi', '--query-gpu=index,name,memory.used,memory.total,utilization.gpu', '--format=csv,noheader,nounits'], capture_output=True, text=True)
            if gpu_result.returncode == 0:
                print("🎮 GPU使用情况:")
                gpu_lines = gpu_result.stdout.strip().split('\n')
                for line in gpu_lines:
                    parts = line.split(', ')
                    if len(parts) >= 5:
                        gpu_id, name, mem_used, mem_total, util = parts
                        print(f"  GPU{gpu_id}: {name} - 内存:{mem_used}MB/{mem_total}MB 利用率:{util}%")
            else:
                print("⚠️ 无法获取GPU信息")
        except FileNotFoundError:
            print("⚠️ nvidia-smi未找到")
        
        print()
        
    except Exception as e:
        print(f"❌ 监控出错: {e}")

def main():
    """主监控循环"""
    print("🚀 启动训练监控")
    print("按Ctrl+C停止监控")
    print()
    
    try:
        while True:
            check_training_processes()
            print("等待30秒后刷新...")
            print("-" * 60)
            time.sleep(30)
    except KeyboardInterrupt:
        print("\n👋 监控已停止")

if __name__ == "__main__":
    main()
