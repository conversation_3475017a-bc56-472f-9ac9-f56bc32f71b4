"""
注意力机制可视化工具
生成注意力权重热力图和分析图表
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
import os
from typing import Dict, Tuple, Optional, List
import yaml

class AttentionVisualizer:
    """注意力可视化器"""
    
    def __init__(self, config_path: str = "configs/attention_config.yaml"):
        """
        初始化可视化器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.viz_config = self.config['visualization']
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建自定义颜色映射
        self.attention_cmap = LinearSegmentedColormap.from_list(
            'attention', ['white', 'lightblue', 'blue', 'darkblue']
        )
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def visualize_agv_task_attention(self, attention_weights: torch.Tensor,
                                   agv_positions: torch.Tensor, task_positions: torch.Tensor,
                                   save_path: Optional[str] = None) -> None:
        """
        可视化AGV-任务注意力权重
        
        Args:
            attention_weights: 注意力权重 [batch_size, num_heads, num_agvs, num_tasks]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            task_positions: 任务位置 [batch_size, num_tasks, 2]
            save_path: 保存路径
        """
        # 取第一个batch和平均注意力权重
        attention = attention_weights[0].mean(dim=0).detach().cpu().numpy()  # [num_agvs, num_tasks]
        agv_pos = agv_positions[0].detach().cpu().numpy()
        task_pos = task_positions[0].detach().cpu().numpy()
        
        num_agvs, num_tasks = attention.shape
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 注意力权重热力图
        ax1 = axes[0, 0]

        # 创建自定义注释矩阵，控制小数点位数
        annot_matrix = np.zeros_like(attention, dtype=object)
        for i in range(attention.shape[0]):
            for j in range(attention.shape[1]):
                val = attention[i, j]
                if val < 1e-6:
                    annot_matrix[i, j] = '0'  # 显示为0而不是科学计数法
                else:
                    annot_matrix[i, j] = f'{val:.2f}'  # 保留2位小数

        sns.heatmap(attention, annot=annot_matrix, fmt='', cmap=self.attention_cmap,
                   xticklabels=[f'T{i}' for i in range(num_tasks)],
                   yticklabels=[f'AGV{i}' for i in range(num_agvs)],
                   ax=ax1)
        ax1.set_title('AGV-Task Attention Weights\n(0 = Blocked by Constraints)')
        ax1.set_xlabel('Tasks (Light:5kg, Heavy:10kg)')
        ax1.set_ylabel('AGVs (Load Status Varies)')
        
        # 2. 地图上的注意力可视化
        ax2 = axes[0, 1]
        
        # 绘制AGV位置
        ax2.scatter(agv_pos[:, 0], agv_pos[:, 1], c='blue', s=100, 
                   marker='o', label='AGVs', alpha=0.8)
        for i, (x, y) in enumerate(agv_pos):
            ax2.annotate(f'AGV{i}', (x, y), xytext=(5, 5), 
                        textcoords='offset points', fontsize=8)
        
        # 绘制任务位置
        ax2.scatter(task_pos[:, 0], task_pos[:, 1], c='red', s=80, 
                   marker='s', label='Tasks', alpha=0.8)
        for i, (x, y) in enumerate(task_pos):
            ax2.annotate(f'T{i}', (x, y), xytext=(5, 5), 
                        textcoords='offset points', fontsize=8)
        
        # 绘制注意力连线 - 降低阈值以显示更多连线
        for i in range(num_agvs):
            for j in range(num_tasks):
                if attention[i, j] > 0.05:  # 降低阈值从0.1到0.05
                    alpha = min(attention[i, j] * 3, 1.0)  # 增加透明度倍数
                    linewidth = max(attention[i, j] * 5, 0.5)  # 增加线宽倍数，设置最小线宽
                    ax2.plot([agv_pos[i, 0], task_pos[j, 0]],
                            [agv_pos[i, 1], task_pos[j, 1]],
                            'g-', alpha=alpha, linewidth=linewidth)
        
        ax2.set_title('Attention Visualization on Map')
        ax2.set_xlabel('X Position')
        ax2.set_ylabel('Y Position')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 每个AGV的注意力分布
        ax3 = axes[1, 0]
        for i in range(num_agvs):
            ax3.plot(attention[i], marker='o', label=f'AGV{i}', alpha=0.7)
        ax3.set_title('Attention Distribution per AGV')
        ax3.set_xlabel('Task Index')
        ax3.set_ylabel('Attention Weight')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 注意力统计
        ax4 = axes[1, 1]
        
        # 计算统计指标
        max_attention = np.max(attention, axis=1)
        attention_entropy = -np.sum(attention * np.log(attention + 1e-8), axis=1)
        attention_concentration = np.max(attention, axis=1)
        
        x_pos = np.arange(num_agvs)
        width = 0.25
        
        ax4.bar(x_pos - width, max_attention, width, label='Max Attention', alpha=0.7)
        ax4.bar(x_pos, attention_entropy / 3, width, label='Entropy (scaled)', alpha=0.7)
        ax4.bar(x_pos + width, attention_concentration, width, label='Concentration', alpha=0.7)
        
        ax4.set_title('Attention Statistics')
        ax4.set_xlabel('AGV Index')
        ax4.set_ylabel('Value')
        ax4.set_xticks(x_pos)
        ax4.set_xticklabels([f'AGV{i}' for i in range(num_agvs)])
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"AGV-任务注意力可视化已保存到: {save_path}")
        
        plt.show()
    
    def visualize_agv_collaboration_attention(self, attention_weights: torch.Tensor,
                                            agv_positions: torch.Tensor,
                                            save_path: Optional[str] = None) -> None:
        """
        可视化AGV协作注意力权重
        
        Args:
            attention_weights: 注意力权重 [batch_size, num_heads, num_agvs, num_agvs]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            save_path: 保存路径
        """
        # 取第一个batch和平均注意力权重
        attention = attention_weights[0].mean(dim=0).detach().cpu().numpy()  # [num_agvs, num_agvs]
        agv_pos = agv_positions[0].detach().cpu().numpy()
        
        num_agvs = attention.shape[0]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 协作注意力权重热力图
        ax1 = axes[0, 0]

        # 创建自定义注释矩阵，控制小数点位数
        annot_matrix = np.zeros_like(attention, dtype=object)
        for i in range(attention.shape[0]):
            for j in range(attention.shape[1]):
                val = attention[i, j]
                if val < 1e-6:
                    annot_matrix[i, j] = '0'  # 显示为0而不是科学计数法
                else:
                    annot_matrix[i, j] = f'{val:.2f}'  # 保留2位小数

        sns.heatmap(attention, annot=annot_matrix, fmt='', cmap=self.attention_cmap,
                   xticklabels=[f'AGV{i}' for i in range(num_agvs)],
                   yticklabels=[f'AGV{i}' for i in range(num_agvs)],
                   ax=ax1)
        ax1.set_title('AGV Collaboration Attention Weights\n(0 = Beyond Collab Radius or Self-Attention)')
        ax1.set_xlabel('Target AGVs')
        ax1.set_ylabel('Source AGVs')
        
        # 2. 地图上的协作可视化
        ax2 = axes[0, 1]
        
        # 绘制AGV位置
        ax2.scatter(agv_pos[:, 0], agv_pos[:, 1], c='blue', s=150, 
                   marker='o', alpha=0.8)
        for i, (x, y) in enumerate(agv_pos):
            ax2.annotate(f'AGV{i}', (x, y), xytext=(5, 5), 
                        textcoords='offset points', fontsize=10, fontweight='bold')
        
        # 绘制协作连线
        for i in range(num_agvs):
            for j in range(num_agvs):
                if i != j and attention[i, j] > 0.1:  # 排除自己，只显示较强的协作
                    alpha = min(attention[i, j] * 2, 1.0)
                    ax2.annotate('', xy=agv_pos[j], xytext=agv_pos[i],
                               arrowprops=dict(arrowstyle='->', alpha=alpha, 
                                             lw=attention[i, j] * 5, color='green'))
        
        ax2.set_title('Collaboration Visualization on Map')
        ax2.set_xlabel('X Position')
        ax2.set_ylabel('Y Position')
        ax2.grid(True, alpha=0.3)
        
        # 3. 协作强度分析
        ax3 = axes[1, 0]
        
        # 计算协作指标
        collaboration_intensity = np.sum(attention, axis=1)  # 每个AGV的总协作强度
        received_attention = np.sum(attention, axis=0)       # 每个AGV接收到的注意力
        
        x_pos = np.arange(num_agvs)
        width = 0.35
        
        ax3.bar(x_pos - width/2, collaboration_intensity, width, 
               label='Given Attention', alpha=0.7)
        ax3.bar(x_pos + width/2, received_attention, width, 
               label='Received Attention', alpha=0.7)
        
        ax3.set_title('Collaboration Intensity')
        ax3.set_xlabel('AGV Index')
        ax3.set_ylabel('Attention Sum')
        ax3.set_xticks(x_pos)
        ax3.set_xticklabels([f'AGV{i}' for i in range(num_agvs)])
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 协作网络图
        ax4 = axes[1, 1]
        
        # 计算AGV之间的距离
        distances = np.zeros((num_agvs, num_agvs))
        for i in range(num_agvs):
            for j in range(num_agvs):
                distances[i, j] = np.linalg.norm(agv_pos[i] - agv_pos[j])
        
        # 绘制距离vs注意力的散点图
        dist_flat = distances.flatten()
        att_flat = attention.flatten()
        
        # 排除对角线元素（自己对自己）
        mask = dist_flat > 0
        dist_filtered = dist_flat[mask]
        att_filtered = att_flat[mask]
        
        ax4.scatter(dist_filtered, att_filtered, alpha=0.6)
        ax4.set_title('Distance vs Attention Relationship')
        ax4.set_xlabel('Distance between AGVs')
        ax4.set_ylabel('Attention Weight')
        ax4.grid(True, alpha=0.3)
        
        # 添加趋势线
        if len(dist_filtered) > 1:
            z = np.polyfit(dist_filtered, att_filtered, 1)
            p = np.poly1d(z)
            ax4.plot(sorted(dist_filtered), p(sorted(dist_filtered)), "r--", alpha=0.8)
        
        plt.tight_layout()
        
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"AGV协作注意力可视化已保存到: {save_path}")
        
        plt.show()
    
    def create_attention_analysis_report(self, output_dict: Dict[str, torch.Tensor],
                                       agv_positions: torch.Tensor, task_positions: torch.Tensor,
                                       save_dir: str = "results/attention_analysis") -> None:
        """
        创建完整的注意力分析报告
        
        Args:
            output_dict: 双层注意力输出
            agv_positions: AGV位置
            task_positions: 任务位置
            save_dir: 保存目录
        """
        os.makedirs(save_dir, exist_ok=True)
        
        # 1. AGV-任务注意力可视化
        agv_task_path = os.path.join(save_dir, "agv_task_attention.png")
        self.visualize_agv_task_attention(
            output_dict['layer1_attention'], agv_positions, task_positions, agv_task_path
        )
        
        # 2. AGV协作注意力可视化
        agv_collab_path = os.path.join(save_dir, "agv_collaboration_attention.png")
        self.visualize_agv_collaboration_attention(
            output_dict['layer2_attention'], agv_positions, agv_collab_path
        )
        
        print(f"🎨 注意力分析报告已生成: {save_dir}")

if __name__ == "__main__":
    # 测试注意力可视化
    print("🎨 测试注意力可视化工具...")
    
    # 创建测试数据
    batch_size = 1
    num_agvs = 4
    num_tasks = 8
    
    # 模拟注意力权重
    layer1_attention = torch.softmax(torch.randn(batch_size, 4, num_agvs, num_tasks), dim=-1)
    layer2_attention = torch.softmax(torch.randn(batch_size, 4, num_agvs, num_agvs), dim=-1)
    
    # 模拟位置
    agv_positions = torch.randint(0, 26, (batch_size, num_agvs, 2)).float()
    task_positions = torch.randint(0, 26, (batch_size, num_tasks, 2)).float()
    
    # 创建可视化器
    visualizer = AttentionVisualizer()
    
    # 测试可视化
    output_dict = {
        'layer1_attention': layer1_attention,
        'layer2_attention': layer2_attention
    }
    
    visualizer.create_attention_analysis_report(
        output_dict, agv_positions, task_positions, "results/test_attention_viz"
    )
    
    print("✅ 注意力可视化工具测试完成！")
