#!/usr/bin/env python3
"""
环境注册器
注册多智能体AGV环境到Ray RLlib
"""

from ray.tune.registry import register_env
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.mappo.multi_agent_env_wrapper import MultiAgentAGVEnv


def register_agv_env():
    """注册多智能体AGV环境"""
    register_env("multi_agent_agv_env", lambda config: MultiAgentAGVEnv(config))
    print("✅ 多智能体AGV环境注册完成: multi_agent_agv_env")


if __name__ == "__main__":
    # 测试环境注册
    print("🌍 测试环境注册...")
    register_agv_env()
    print("🎉 环境注册测试完成！")