#!/usr/bin/env python3
"""
集成双层注意力机制的MAPPO价值网络
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
from ray.rllib.models.torch.torch_modelv2 import TorchModelV2
from ray.rllib.utils.annotations import override
from ray.rllib.utils.typing import ModelConfigDict, TensorType
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from src.attention.dual_layer_attention import DualLayerAttention


class AttentionValueNetwork(TorchModelV2, nn.Module):
    """
    集成双层注意力机制的价值网络

    网络架构：
    1. 观察空间解析 -> AGV特征 + 任务特征
    2. 双层注意力机制 -> 注意力增强特征
    3. 特征融合 -> 价值估计
    """

    def __init__(self, obs_space, action_space, num_outputs, model_config, name):
        """
        初始化注意力价值网络

        Args:
            obs_space: 观察空间
            action_space: 动作空间
            num_outputs: 输出维度（价值网络通常为1）
            model_config: 模型配置
            name: 网络名称
        """
        TorchModelV2.__init__(self, obs_space, action_space, num_outputs, model_config, name)
        nn.Module.__init__(self)

        # 保存配置
        self.obs_space = obs_space
        self.action_space = action_space
        self.num_outputs = num_outputs
        self.model_config = model_config

        # 从配置中获取参数
        custom_config = model_config.get("custom_model_config", {})
        self.max_agvs = custom_config.get("max_agvs", 4)
        self.max_tasks = custom_config.get("max_tasks", 16)
        self.agv_feature_dim = custom_config.get("agv_feature_dim", 5)
        self.task_feature_dim = custom_config.get("task_feature_dim", 4)
        self.attention_config_path = custom_config.get("attention_config_path", "configs/attention_config.yaml")

        # 计算观察空间维度
        self.obs_dim = obs_space.shape[0]
        expected_obs_dim = self.max_tasks * self.task_feature_dim + self.max_agvs * self.agv_feature_dim

        if self.obs_dim != expected_obs_dim:
            raise ValueError(f"观察空间维度不匹配: 期望{expected_obs_dim}, 实际{self.obs_dim}")

        # 初始化双层注意力机制
        self.dual_attention = DualLayerAttention(self.attention_config_path)

        # 特征提取网络
        self.feature_extractor = nn.Sequential(
            nn.Linear(self.obs_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 128),
            nn.ReLU(),
            nn.Dropout(0.1)
        )

        # 注意力特征融合网络
        attention_output_dim = 64  # 双层注意力输出维度
        combined_dim = 128 + attention_output_dim  # 基础特征 + 注意力特征

        self.fusion_network = nn.Sequential(
            nn.Linear(combined_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU()
        )

        # 价值头（输出状态价值）
        self.value_head = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )

        # 初始化权重
        self._initialize_weights()

    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)

    def _parse_observation(self, obs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        解析观察空间，提取AGV和任务特征

        Args:
            obs: 观察张量 [batch_size, obs_dim]

        Returns:
            tuple: (agv_features, task_features, agv_positions, task_positions,
                   agv_loads, agv_capacities, task_weights, task_states)
        """
        batch_size = obs.shape[0]

        # 分离任务和AGV特征
        task_features_flat = obs[:, :self.max_tasks * self.task_feature_dim]  # [batch_size, 64]
        agv_features_flat = obs[:, self.max_tasks * self.task_feature_dim:]   # [batch_size, 20]

        # 重塑任务特征 [batch_size, max_tasks, task_feature_dim]
        task_features = task_features_flat.view(batch_size, self.max_tasks, self.task_feature_dim)

        # 重塑AGV特征 [batch_size, max_agvs, agv_feature_dim]
        agv_features = agv_features_flat.view(batch_size, self.max_agvs, self.agv_feature_dim)

        # 提取具体特征
        # 任务特征: [x, y, weight, state]
        task_positions = task_features[:, :, :2]  # [batch_size, max_tasks, 2]
        task_weights = task_features[:, :, 2]     # [batch_size, max_tasks]
        task_states = task_features[:, :, 3]      # [batch_size, max_tasks]

        # AGV特征: [x, y, load, capacity, queue_length]
        agv_positions = agv_features[:, :, :2]    # [batch_size, max_agvs, 2]
        agv_loads = agv_features[:, :, 2]         # [batch_size, max_agvs]
        agv_capacities = agv_features[:, :, 3]    # [batch_size, max_agvs]

        return (agv_features, task_features, agv_positions, task_positions,
                agv_loads, agv_capacities, task_weights, task_states)

    @override(TorchModelV2)
    def forward(self, input_dict: Dict[str, TensorType], state: List[TensorType],
                seq_lens: TensorType) -> Tuple[TensorType, List[TensorType]]:
        """
        前向传播

        Args:
            input_dict: 输入字典，包含观察
            state: RNN状态（未使用）
            seq_lens: 序列长度（未使用）

        Returns:
            tuple: (values, state)
        """
        obs = input_dict["obs"]

        # 解析观察空间
        (agv_features, task_features, agv_positions, task_positions,
         agv_loads, agv_capacities, task_weights, task_states) = self._parse_observation(obs)

        # 基础特征提取
        base_features = self.feature_extractor(obs)  # [batch_size, 128]

        # 双层注意力机制
        attention_output = self.dual_attention(
            agv_features, task_features, agv_positions, task_positions,
            agv_loads, agv_capacities, task_weights, task_states
        )

        # 获取融合后的注意力特征
        attention_features = attention_output['fused_features']  # [batch_size, max_agvs, 64]

        # 对AGV维度进行平均池化，得到全局注意力特征
        global_attention_features = attention_features.mean(dim=1)  # [batch_size, 64]

        # 特征融合
        combined_features = torch.cat([base_features, global_attention_features], dim=-1)  # [batch_size, 192]
        fused_features = self.fusion_network(combined_features)  # [batch_size, 64]

        # 价值估计
        values = self.value_head(fused_features).squeeze(-1)  # [batch_size]

        # 保存特征用于后续使用
        self._features = fused_features

        return values, state

    @override(TorchModelV2)
    def value_function(self) -> TensorType:
        """
        计算状态价值函数

        Returns:
            torch.Tensor: 状态价值 [batch_size]
        """
        if not hasattr(self, '_features'):
            raise ValueError("必须先调用forward()方法")

        return self.value_head(self._features).squeeze(-1)  # [batch_size]

    def get_attention_weights(self, obs: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        获取注意力权重用于分析

        Args:
            obs: 观察张量

        Returns:
            dict: 注意力权重字典
        """
        # 解析观察空间
        (agv_features, task_features, agv_positions, task_positions,
         agv_loads, agv_capacities, task_weights, task_states) = self._parse_observation(obs)

        # 获取注意力输出
        attention_output = self.dual_attention(
            agv_features, task_features, agv_positions, task_positions,
            agv_loads, agv_capacities, task_weights, task_states
        )

        return {
            'layer1_attention': attention_output['layer1_attention'],
            'layer2_attention': attention_output['layer2_attention'],
            'fusion_weights': attention_output['fusion_weights']
        }


def create_attention_value_network(obs_space, action_space, num_outputs, model_config, name):
    """
    创建注意力价值网络的工厂函数

    Args:
        obs_space: 观察空间
        action_space: 动作空间
        num_outputs: 输出维度
        model_config: 模型配置
        name: 网络名称

    Returns:
        AttentionValueNetwork: 注意力价值网络实例
    """
    return AttentionValueNetwork(obs_space, action_space, num_outputs, model_config, name)


if __name__ == "__main__":
    # 测试网络
    print("💰 测试注意力价值网络...")

    import gymnasium as gym
    from gymnasium.spaces import Box

    # 创建模拟观察空间和动作空间
    obs_dim = 16 * 4 + 4 * 5  # 16个任务×4特征 + 4个AGV×5特征 = 84
    obs_space = Box(low=-np.inf, high=np.inf, shape=(obs_dim,))
    action_space = gym.spaces.Discrete(5)  # 5个动作

    # 模型配置
    model_config = {
        "custom_model_config": {
            "max_agvs": 4,
            "max_tasks": 16,
            "agv_feature_dim": 5,
            "task_feature_dim": 4,
            "attention_config_path": "configs/attention_config.yaml"
        }
    }

    # 创建网络
    network = AttentionValueNetwork(obs_space, action_space, 1, model_config, "test")

    # 创建测试输入
    batch_size = 2
    test_obs = torch.randn(batch_size, obs_dim)
    input_dict = {"obs": test_obs}

    # 前向传播
    values, _ = network.forward(input_dict, [], None)

    print(f"✅ 网络测试成功")
    print(f"  输入形状: {test_obs.shape}")
    print(f"  价值输出形状: {values.shape}")

    # 测试注意力权重
    attention_weights = network.get_attention_weights(test_obs)
    print(f"  第一层注意力形状: {attention_weights['layer1_attention'].shape}")
    print(f"  第二层注意力形状: {attention_weights['layer2_attention'].shape}")

    print("🎉 注意力价值网络实现完成！")