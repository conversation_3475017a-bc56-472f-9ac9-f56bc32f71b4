# 双层注意力机制MAPPO多AGV调度系统

## 项目概述

本项目是一个完整的毕业设计项目，实现了基于双层注意力机制的MAPPO算法用于多AGV协作调度。项目包含完整的算法实现、训练系统、可视化工具和验证脚本。

## 核心特性

### 🧠 双层注意力机制
- **AGV-任务注意力**: 帮助每个AGV关注最相关的任务
- **AGV协作注意力**: 促进智能体间的协调合作
- **动态权重分配**: 根据环境状态自适应调整注意力权重

### 🤖 MAPPO算法集成
- 完全集成到Ray RLlib框架
- 支持多智能体策略优化
- 端到端梯度传播和参数优化

### 📚 课程学习
- 12阶段渐进式训练：从(1,1)到(4,16)AGV和任务配置
- 自动阶段转换逻辑
- 智能难度递增策略

### 📊 完整可视化系统
- 环境状态实时可视化
- 注意力权重热力图
- 训练过程监控
- 性能指标追踪

## 项目结构

```
├── src/                          # 核心源代码
│   ├── attention/               # 双层注意力机制
│   │   ├── agv_task_attention.py
│   │   ├── agv_collaboration_attention.py
│   │   └── dual_layer_attention.py
│   ├── environment/             # AGV仓储环境
│   │   ├── agv_warehouse_env.py
│   │   ├── agv.py
│   │   ├── map_generator.py
│   │   └── pathfinding.py
│   ├── mappo/                   # MAPPO算法实现
│   │   ├── attention_policy_network.py
│   │   ├── attention_value_network.py
│   │   ├── curriculum_trainer.py
│   │   └── multi_agent_env_wrapper.py
│   └── visualization/           # 可视化系统
│       ├── curriculum_stage_visualizer.py
│       ├── attention_visualizer.py
│       └── training_monitor.py
├── configs/                     # 配置文件
│   ├── attention_config.yaml
│   ├── environment_config.yaml
│   └── mappo_config.yaml
├── scripts/                     # 脚本工具
│   ├── generate_curriculum_visualization.py
│   ├── hyperparameter_training.py
│   └── run_main_experiment.py
├── results/                     # 实验结果
└── requirements.txt             # 依赖包
```

## 快速开始

### 环境配置
```bash
# 创建虚拟环境
conda create -n biye_RL python=3.8
conda activate biye_RL

# 安装依赖
pip install -r requirements.txt
```

### 运行训练
```bash
# 基础训练
python src/mappo/curriculum_trainer.py

# 超参数优化训练
python scripts/hyperparameter_training.py

# 生成可视化
python scripts/generate_curriculum_visualization.py
```

### 验证系统
```bash
# 快速验证
python scripts/quick_validation.py

# 完整验证
python scripts/validate_mappo_integration.py
```

## 技术架构

### 算法架构
```
MAPPO算法
├── 双层注意力策略网络
│   ├── AGV-任务注意力层
│   └── AGV-AGV协作注意力层
├── 双层注意力价值网络
│   ├── 共享注意力特征提取
│   └── 独立价值函数估计
└── 多智能体环境接口
    ├── 观察空间映射
    └── 动作空间定义
```

### 课程学习阶段
1. (1,1) - 基础单AGV单任务
2. (1,2) - 单AGV多任务选择
3. (1,4) - 单AGV复杂任务管理
4. (2,4) - 双AGV协作基础
5. (2,6) - 双AGV中等负载
6. (2,8) - 双AGV高负载
7. (3,8) - 三AGV协作基础
8. (3,10) - 三AGV中等负载
9. (3,12) - 三AGV高负载
10. (4,12) - 四AGV协作基础
11. (4,14) - 四AGV中等负载
12. (4,16) - 四AGV完整场景

## 实验结果

### 验证完成状态
- ✅ 核心模块导入: 100%成功
- ✅ 配置系统: 12个课程阶段正常
- ✅ 环境包装器: 多智能体接口正常
- ✅ 模型注册: 注意力网络注册成功
- ✅ 课程学习: 训练器初始化成功
- ✅ 可视化系统: 所有组件正常工作

### 可视化成果
- 📸 静态场景图: 12个高质量PNG文件
- 🎬 动态演示: 12个流畅GIF动画
- 📊 训练监控: 实时性能追踪
- 🔥 注意力可视化: 权重热力图

## 配置说明

### 注意力机制配置 (configs/attention_config.yaml)
```yaml
attention:
  hidden_dim: 128
  num_heads: 8
  dropout: 0.1
  temperature: 1.0
```

### 环境配置 (configs/environment_config.yaml)
```yaml
curriculum_stages:
  stage_1: {agv_num: 1, task_num: 1, max_steps: 100}
  stage_2: {agv_num: 1, task_num: 2, max_steps: 150}
  # ... 更多阶段配置
```

### MAPPO配置 (configs/mappo_config.yaml)
```yaml
algorithm:
  learning_rate: 0.0003
  gamma: 0.99
  gae_lambda: 0.95
  clip_param: 0.2
```

## 开发团队

- **项目负责人**: Zhang Chao
- **指导老师**: [导师姓名]
- **项目类型**: 毕业设计
- **完成时间**: 2025年6月

## 许可证

本项目仅用于学术研究和教育目的。

## 联系方式

如有问题或建议，请联系：
- 邮箱: <EMAIL>
- GitHub: https://github.com/3145647405/biye_augment

---

**注意**: 本项目已完成所有核心功能的开发和验证，可直接用于毕业设计答辩和学术展示。
